<!DOCTYPE html>
<html lang="ko">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>윈도우 11 Shift 키 입력 문제 해결</title>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reset.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reveal.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/theme/white.min.css" id="theme">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@300;400;500;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

<style>
:root{
  --font-body:'Noto Sans KR', sans-serif;
  --font-heading:'Roboto', sans-serif;
  --primary:#004488;
  --stage1:#3b82f6;--stage1-dark:#1e40af;
  --stage2:#10b981;--stage2-dark:#047857;
  --stage3:#8b5cf6;--stage3-dark:#6d28d9;
  --stage4:#ef4444;--stage4-dark:#b91c1c;
}
body {
  font-family: var(--font-body);
}
.reveal h1, .reveal h2, .reveal h3, .reveal h4, .reveal h5, .reveal h6 {
  font-family: var(--font-heading);
  color: #333;
}
.reveal .slides section {
  text-align: left;
  padding: 20px;
  box-sizing: border-box;
}
.reveal .slide-header-custom {
  background-color: var(--primary);
  color: white;
  padding: 15px 25px;
  border-radius: 8px 8px 0 0;
  margin-bottom:0 !important;
}
.reveal .slide-header-custom h1 {
  font-size: 1.8em;
  margin: 0;
  color: white;
  display: flex;
  align-items: center;
}
.reveal .slide-header-custom p.subtitle {
  font-size: 0.9em;
  opacity: 0.8;
  margin-top: 5px;
}
.reveal .slide-content-custom {
  padding: 20px;
  font-size: 0.85em;
  line-height: 1.6;
  overflow-y: auto;
  height: calc(100% - 70px); /* Adjust based on header height */
}
.reveal .slide-content-custom.align-top {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}
.reveal .slide-content-custom.text-center {
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.reveal .slide-content-custom ul {
  list-style-type: disc;
  margin-left: 25px;
}
.reveal .slide-content-custom li {
  margin-bottom: 0.5em;
}
.reveal .slide-content-custom p {
  margin-bottom: 0.8em;
}
.reveal .slide-content-custom pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.reveal .slide-content-custom code {
  font-family: 'Courier New', Courier, monospace;
}
.stage-number {
  display: inline-block;
  border-radius: 50%;
  background: rgba(255,255,255,.2);
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  margin-right: 10px;
  font-size: .8em;
}
.stage-1-grad{background:linear-gradient(135deg,var(--stage1) 0%,var(--stage1-dark) 100%)}
.stage-2-grad{background:linear-gradient(135deg,var(--stage2) 0%,var(--stage2-dark) 100%)}
.stage-3-grad{background:linear-gradient(135deg,var(--stage3) 0%,var(--stage3-dark) 100%)}
.stage-4-grad{background:linear-gradient(135deg,var(--stage4) 0%,var(--stage4-dark) 100%)}

.reveal .slide-content-custom .text-sm { font-size: 0.9em; }
.reveal .slide-content-custom .font-semibold { font-weight: 600; }
.reveal .slide-content-custom .mt-2 { margin-top: 0.5rem; }
.reveal .slide-content-custom .mb-1 { margin-bottom: 0.25rem; }
.reveal .slide-content-custom .text-red-600 { color: #dc2626; }
.reveal .slide-content-custom .bg-gray-100 { background-color: #f3f4f6; }
.reveal .slide-content-custom .p-2 { padding: 0.5rem; }
.reveal .slide-content-custom .rounded { border-radius: 0.25rem; }
</style>
</head>
<body>
<div class="reveal">
<div class="slides">
<section>
  <div class="slide-header-custom">
    <h1>윈도우 11 Shift 키 입력 문제 해결</h1>
    <p class="subtitle">다양한 원인과 단계별 해결 방법</p>
  </div>
  <div class="slide-content-custom text-center">
    <p style="font-size:1.2em; margin-top: 20px;">윈도우 11 환경에서 Shift 키가 정상적으로 작동하지 않을 때<br> 시도해 볼 수 있는 문제 해결 과정을 안내합니다.</p>
    <i class="fas fa-keyboard fa-3x" style="margin-top: 30px; color: var(--primary);"></i>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-1-grad">
    <h1><span class="stage-number">1</span>기본적인 확인 사항</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>가장 먼저 시도해 볼 조치</h2>
    <ul>
      <li><strong>재부팅:</strong> 컴퓨터를 완전히 종료했다가 다시 켜보세요. 많은 간단한 문제들이 재부팅으로 해결될 수 있습니다.</li>
      <li><strong>다른 프로그램에서 테스트:</strong>
        <ul>
          <li>특정 프로그램(예: 워드, 게임)에서만 문제가 발생하는지 확인합니다.</li>
          <li>메모장, 웹 브라우저 등 여러 프로그램에서 Shift 키를 테스트하여 문제 범위를 파악합니다.</li>
          <li>특정 프로그램 문제인 경우, 해당 프로그램의 설정이나 단축키 충돌 여부를 확인해야 합니다.</li>
        </ul>
      </li>
    </ul>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-2-grad">
    <h1><span class="stage-number">2</span>물리적 키보드 점검</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>키보드 자체의 문제인지 확인</h2>
    <ul>
      <li><strong>키보드 청소:</strong>
        <ul>
          <li>Shift 키 주변에 먼지, 이물질 등이 끼어있을 수 있습니다.</li>
          <li>키보드를 뒤집어 가볍게 털거나, 에어 스프레이, 부드러운 솔을 사용하여 청소합니다.</li>
        </ul>
      </li>
      <li><strong>키캡 확인:</strong>
        <ul>
          <li>Shift 키캡이 제대로 장착되어 있는지, 키캡이나 내부 스위치에 파손된 부분은 없는지 육안으로 확인합니다.</li>
        </ul>
      </li>
    </ul>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-3-grad">
    <h1><span class="stage-number">3</span>물리적 키보드 점검 (연결)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>키보드 연결 상태 확인</h2>
    <ul>
      <li><strong>케이블 연결 확인 (유선 키보드):</strong>
        <ul>
          <li>USB 포트에서 케이블을 분리했다가 다시 단단히 연결해 보세요.</li>
          <li>컴퓨터의 다른 USB 포트에 연결하여 포트 문제를 배제합니다.</li>
        </ul>
      </li>
      <li><strong>배터리 확인 (무선 키보드):</strong>
        <ul>
          <li>배터리가 부족하면 키 입력이 불안정하거나 특정 키가 작동하지 않을 수 있습니다.</li>
          <li>배터리를 새 것으로 교체하거나 완전히 충전해 보세요.</li>
          <li>무선 수신기의 연결 상태도 확인합니다.</li>
        </ul>
      </li>
    </ul>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-4-grad">
    <h1><span class="stage-number">4</span>Windows 설정 확인: 고정 키</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>고정 키 (Sticky Keys) 기능 비활성화</h2>
    <p>고정 키 기능이 의도치 않게 활성화되면 Shift, Ctrl, Alt 키가 한 번 누르면 계속 눌린 상태로 인식될 수 있습니다.</p>
    <ol>
      <li><strong>설정 앱 열기:</strong> <code>Windows 키 + I</code> 를 누릅니다.</li>
      <li><strong>접근성 메뉴 이동:</strong> 왼쪽 메뉴에서 '접근성'을 선택합니다.</li>
      <li><strong>키보드 설정 확인:</strong> '키보드' 항목으로 이동합니다.</li>
      <li><strong>고정 키 비활성화:</strong> '고정 키' 항목을 찾아 <span class="font-semibold">끔</span>으로 설정되어 있는지 확인합니다. '켬'으로 되어 있다면 '끔'으로 변경합니다.</li>
    </ol>
    <img src="https://i.imgur.com/exampleStickyKeys.png" alt="고정 키 설정 예시" style="max-width: 70%; margin-top:15px; border:1px solid #ccc; display:block; margin-left:auto; margin-right:auto;">
    <p class="text-sm text-center mt-2">이미지: 고정 키 설정 화면 (실제 UI와 다를 수 있음)</p>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-1-grad">
    <h1><span class="stage-number">5</span>Windows 설정 확인: 필터 키</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>필터 키 (Filter Keys) 기능 비활성화</h2>
    <p>필터 키는 짧거나 반복적인 키 입력을 무시하는 기능으로, 이 기능이 Shift 키 입력에 문제를 일으킬 수 있습니다.</p>
    <ol>
      <li><strong>설정 앱 열기:</strong> <code>Windows 키 + I</code> 를 누릅니다.</li>
      <li><strong>접근성 메뉴 이동:</strong> '접근성' > '키보드'로 이동합니다.</li>
      <li><strong>필터 키 비활성화:</strong> '필터 키' 항목을 찾아 <span class="font-semibold">끔</span>으로 설정되어 있는지 확인합니다. 만약 '켬'으로 되어 있다면 '끔'으로 변경합니다.</li>
    </ol>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-2-grad">
    <h1><span class="stage-number">6</span>소프트웨어 및 드라이버 문제 해결 (1)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>화상 키보드 테스트</h2>
    <p>물리적 키보드의 문제인지, 소프트웨어적 문제인지 감별하는 데 도움이 됩니다.</p>
    <ol>
      <li><strong>화상 키보드 실행:</strong>
        <ul>
          <li>작업 표시줄 검색창에 '화상 키보드' 또는 'osk'를 입력하고 실행합니다.</li>
        </ul>
      </li>
      <li><strong>Shift 키 테스트:</strong>
        <ul>
          <li>화상 키보드에 나타난 Shift 키를 마우스로 클릭하여 입력이 되는지 확인합니다.</li>
          <li>화상 키보드에서 Shift 키가 정상 작동한다면, 물리적 키보드의 하드웨어나 드라이버 문제일 가능성이 높습니다.</li>
          <li>화상 키보드에서도 문제가 있다면, Windows 시스템 파일 또는 다른 소프트웨어 충돌일 수 있습니다.</li>
        </ul>
      </li>
    </ol>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-3-grad">
    <h1><span class="stage-number">7</span>소프트웨어 및 드라이버 문제 해결 (2)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>다른 키보드로 테스트</h2>
    <p>가장 확실하게 현재 키보드의 하드웨어 고장 여부를 판단할 수 있는 방법 중 하나입니다.</p>
    <ul>
      <li>가능하다면, 정상 작동하는 다른 물리적 키보드를 현재 컴퓨터에 연결하여 Shift 키가 작동하는지 확인합니다.</li>
      <li>다른 키보드에서는 Shift 키가 정상적으로 입력된다면, 현재 사용 중인 키보드의 하드웨어 고장일 가능성이 매우 큽니다.</li>
      <li>다른 키보드에서도 동일한 문제가 발생한다면, 컴퓨터의 소프트웨어, 드라이버 또는 USB 포트 등의 문제일 가능성이 있습니다.</li>
    </ul>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-4-grad">
    <h1><span class="stage-number">8</span>소프트웨어 및 드라이버 문제 해결 (3)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>키보드 드라이버 업데이트 또는 재설치</h2>
    <ol>
      <li class="mb-1"><strong>장치 관리자 실행:</strong> <code>Windows 키 + X</code>를 누르고 '장치 관리자'를 선택합니다.</li>
      <li class="mb-1"><strong>키보드 항목 확장:</strong> 목록에서 '키보드' 항목을 찾아 확장합니다.</li>
      <li class="mb-1"><strong>드라이버 업데이트:</strong>
        <ul>
          <li>사용 중인 키보드 장치(예: "HID 키보드 장치")를 마우스 오른쪽 버튼으로 클릭합니다.</li>
          <li>'드라이버 업데이트'를 선택하고, '자동으로 드라이버 검색' 옵션을 선택합니다.</li>
        </ul>
      </li>
      <li class="mb-1"><strong>드라이버 재설치 (업데이트로 해결되지 않을 경우):</strong>
        <ul>
          <li>다시 키보드 장치를 마우스 오른쪽 버튼으로 클릭하고 '디바이스 제거'를 선택합니다.</li>
          <li>"이 장치의 드라이버 소프트웨어를 삭제합니다." 옵션이 있다면 체크하고 제거합니다.</li>
          <li>컴퓨터를 재부팅하면 Windows가 자동으로 키보드 드라이버를 다시 설치합니다.</li>
        </ul>
      </li>
    </ol>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-1-grad">
    <h1><span class="stage-number">9</span>소프트웨어 및 드라이버 문제 해결 (4)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>최근 설치 프로그램 또는 Windows 업데이트 확인</h2>
    <p>문제가 발생하기 직전에 특정 작업이 있었다면 원인일 수 있습니다.</p>
    <ul>
      <li><strong>최근 설치한 프로그램 제거:</strong>
        <ul>
          <li>문제가 발생하기 직전에 설치한 프로그램이 있다면, 해당 프로그램과의 충돌일 수 있습니다. '제어판' > '프로그램 및 기능'에서 해당 프로그램을 제거해 보세요.</li>
        </ul>
      </li>
      <li><strong>Windows 업데이트 롤백 (신중하게 진행):</strong>
        <ul>
          <li>최근 Windows 업데이트 이후 문제가 발생했다면, 업데이트 오류일 수 있습니다.</li>
          <li>'설정' > 'Windows 업데이트' > '업데이트 기록' > '업데이트 제거'에서 최근 업데이트를 제거해 볼 수 있습니다.</li>
          <li class="text-red-600">단, 시스템 안정성에 영향을 줄 수 있으므로 신중하게 진행해야 합니다.</li>
        </ul>
      </li>
    </ul>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-2-grad">
    <h1><span class="stage-number">10</span>시스템 파일 검사</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>시스템 파일 손상 복구 시도</h2>
    <p>손상된 시스템 파일이 Shift 키 문제를 포함한 다양한 오류의 원인이 될 수 있습니다.</p>
    <ol>
      <li><strong>명령 프롬프트 (관리자 권한) 실행:</strong>
        <ul>
          <li>작업 표시줄 검색창에 <code>cmd</code>를 입력합니다.</li>
          <li>'명령 프롬프트' 앱에 마우스 오른쪽 버튼을 클릭하고 '관리자 권한으로 실행'을 선택합니다.</li>
        </ul>
      </li>
      <li><strong>시스템 파일 검사기 실행:</strong>
        <ul>
          <li>명령 프롬프트 창에 다음 명령어를 입력하고 Enter 키를 누릅니다:
            <pre><code class="bg-gray-100 p-2 rounded block">sfc /scannow</code></pre>
          </li>
        </ul>
      </li>
      <li><strong>검사 완료 후 재부팅:</strong> 검사가 완료될 때까지 기다린 후, 컴퓨터를 재부팅합니다.</li>
    </ol>
  </div>
</section>
<section>
  <div class="slide-header-custom stage-3-grad">
    <h1><span class="stage-number">11</span>클린 부팅</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>백그라운드 프로그램 충돌 확인</h2>
    <p>다른 프로그램과의 충돌이 원인인지 확인하기 위해 최소한의 드라이버와 시작 프로그램으로 Windows를 시작합니다.</p>
    <ol>
      <li><strong>시스템 구성 실행:</strong> 작업 표시줄 검색창에 <code>msconfig</code>를 입력하고 '시스템 구성'을 실행합니다.</li>
      <li><strong>서비스 중지:</strong>
        <ul>
          <li>'서비스' 탭으로 이동합니다.</li>
          <li>'모든 Microsoft 서비스 숨기기'를 체크합니다.</li>
          <li>'모두 사용 안 함'을 클릭합니다.</li>
        </ul>
      </li>
      <li><strong>시작 프로그램 중지:</strong>
        <ul>
          <li>'시작 프로그램' 탭으로 이동하고 '작업 관리자 열기'를 클릭합니다.</li>
          <li>작업 관리자 내 '시작 앱' (또는 '시작프로그램')에서 모든 프로그램을 '사용 안 함'으로 변경합니다.</li>
        </ul>
      </li>
      <li><strong>재부팅 및 확인:</strong> 컴퓨터를 재부팅하여 Shift 키 문제가 해결되었는지 확인합니다.
        <ul>
          <li>문제가 해결되었다면, 비활성화했던 서비스나 시작 프로그램을 하나씩 다시 활성화하면서 원인이 되는 프로그램을 찾아냅니다.</li>
        </ul>
      </li>
    </ol>
  </div>
</section>
<section>
  <div class="slide-header-custom" style="background-color: var(--primary);">
     <h1>결론 및 추가 조치</h1>
  </div>
  <div class="slide-content-custom align-top">
    <h2>최후의 점검 및 전문가 문의</h2>
    <p>위의 모든 단계를 시도했음에도 불구하고 Shift 키 문제가 지속된다면, 다음과 같은 가능성을 고려해야 합니다:</p>
    <ul>
      <li><strong>키보드 하드웨어 고장:</strong> 이 가능성이 가장 높습니다. 특히 다른 키보드로 테스트했을 때 정상 작동했다면 더욱 그렇습니다. 이 경우 키보드를 수리하거나 새 것으로 교체해야 합니다.</li>
      <li><strong>메인보드 USB 포트 문제:</strong> 드물지만, 특정 USB 포트나 메인보드 자체의 문제일 수도 있습니다.</li>
      <li><strong>심각한 운영체제 손상:</strong> 시스템 파일 검사로도 해결되지 않는 경우, Windows 재설치(초기화)를 고려해야 할 수 있습니다. (데이터 백업 필수)</li>
    </ul>
    <p class="mt-2">문제가 해결된 단계나, 특정 상황(예: 특정 게임 실행 시에만 발생)에 대한 추가 정보가 있다면 더 구체적인 원인 파악 및 해결에 도움이 될 수 있습니다.</p>
    <p class="mt-2">필요하다면 컴퓨터 전문가의 도움을 받는 것을 권장합니다.</p>
  </div>
</section>
</div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reveal.min.js"></script>
<script>
Reveal.initialize({
  controls: true,
  progress: true,
  history: true,
  center: false,
  hash: true,
  slideNumber: "c/t",
  transition: "slide",
  backgroundTransition: "fade",
  width: "100%",
  height: "100%",
  margin: 0.05,
  autoSlideStoppable: true,
  hideInactiveCursor: true,
  hideCursorTime: 2000
});
</script>
</body>
</html>