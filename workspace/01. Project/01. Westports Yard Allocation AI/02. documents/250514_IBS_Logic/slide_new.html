<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IBS Core Computational Logic - Impress.js Presentation</title>
    
    <!-- 외부 라이브러리 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">
    
    <style>
        /* 기본 스타일 */
        body {
            font-family: 'Open Sans', sans-serif;
            color: #374151;
            background: #f0f2f5;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        /* 단계별 색상 코드 */
        .stage-1-color { color: #2563eb; } /* Blue */
        .stage-2-color { color: #059669; } /* Green */
        .stage-3-color { color: #7c3aed; } /* Purple */
        .stage-4-color { color: #dc2626; } /* Red */
        
        /* 슬라이드 스타일 */
        .step {
            width: 900px;
            height: 700px;
            padding: 40px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
            transition: opacity 0.5s;
        }
        
        /* 헤더 스타일 */
        .slide-header {
            padding: 15px 25px;
            border-radius: 8px 8px 0 0;
            color: white;
            margin-bottom: 20px;
        }
        
        /* 단계별 헤더 배경 */
        .stage-1-header { background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); }
        .stage-2-header { background: linear-gradient(135deg, #10b981 0%, #047857 100%); }
        .stage-3-header { background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%); }
        .stage-4-header { background: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%); }
        
        /* 내용 컨테이너 */
        .slide-content {
            padding: 20px;
            height: calc(100% - 100px);
            overflow: auto;
        }
        
        /* 정보 박스 */
        .info-box {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        /* 흐름도 */
        .flow-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
        }
        
        .flow-step {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .flow-arrow {
            font-size: 1.2rem;
            color: #94a3b8;
        }
        
        /* 기타 스타일 */
        h1, h2, h3, h4 {
            font-family: 'Roboto', sans-serif;
            margin-top: 0;
        }
        
        .text-center {
            text-align: center;
        }
        
        .icon-large {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div id="impress">
        <!-- 오버뷰 -->
        <div id="overview" class="step" data-x="0" data-y="0" data-scale="4">
            <h1 class="text-center" style="margin-top: 250px; font-size: 2.5rem;">IBS Core Computational Logic</h1>
            <h3 class="text-center" style="font-size: 1.5rem;">A 4-Stage Pipeline for Optimal Rehandling Suggestions</h3>
            
            <div class="flow-container" style="margin-top: 50px;">
                <div class="flow-step">
                    <i class="fas fa-filter stage-1-color icon-large"></i>
                    <p>Stage 1:<br>Candidate Identification</p>
                </div>
                <i class="fas fa-arrow-right flow-arrow"></i>
                <div class="flow-step">
                    <i class="fas fa-map-marked-alt stage-2-color icon-large"></i>
                    <p>Stage 2:<br>Destination Selection</p>
                </div>
                <i class="fas fa-arrow-right flow-arrow"></i>
                <div class="flow-step">
                    <i class="fas fa-clipboard-check stage-3-color icon-large"></i>
                    <p>Stage 3:<br>Candidate Scoring</p>
                </div>
                <i class="fas fa-arrow-right flow-arrow"></i>
                <div class="flow-step">
                    <i class="fas fa-tasks stage-4-color icon-large"></i>
                    <p>Stage 4:<br>Job Selection</p>
                </div>
            </div>
        </div>
        
        <!-- 단계 1: 후보 식별 -->
        <div id="stage1" class="step" data-x="-1000" data-y="-500" data-rotate="0" data-scale="1">
            <div class="slide-header stage-1-header">
                <h2>Stage 1: Candidate Identification & Filtering</h2>
            </div>
            <div class="slide-content">
                <!-- 단계 1 내용 -->
                <div class="text-center">
                    <i class="fas fa-filter stage-1-color icon-large"></i>
                    <h3>Objective: Identify potential rehandles, filter ineligibles, and assign initial priority.</h3>
                </div>
                
                <!-- 흐름도 -->
                <div class="flow-container">
                    <div class="flow-step">
                        <i class="fas fa-cubes stage-1-color"></i>
                        <span>All Containers</span>
                    </div>
                    <i class="fas fa-arrow-right flow-arrow"></i>
                    <div class="flow-step">
                        <i class="fas fa-search-plus stage-1-color"></i>
                        <span>Identify Triggers</span>
                    </div>
                    <i class="fas fa-arrow-right flow-arrow"></i>
                    <div class="flow-step">
                        <i class="fas fa-star-half-alt stage-1-color"></i>
                        <span>Prioritize</span>
                    </div>
                    <i class="fas fa-arrow-right flow-arrow"></i>
                    <div class="flow-step">
                        <i class="fas fa-check-double stage-1-color"></i>
                        <span>Filtered Candidates</span>
                    </div>
                </div>
                
                <!-- 정보 박스 -->
                <div class="info-box">
                    <h4><i class="fas fa-exclamation-triangle stage-1-color"></i> Identification Triggers</h4>
                    <!-- 내용 추가 -->
                </div>
                
                <div class="info-box">
                    <h4><i class="fas fa-sort-amount-down stage-1-color"></i> Initial Prioritization</h4>
                    <!-- 내용 추가 -->
                </div>
                
                <div class="info-box">
                    <h4><i class="fas fa-ban stage-1-color"></i> Exclusion Rules</h4>
                    <!-- 내용 추가 -->
                </div>
            </div>
        </div>
        
        <!-- 단계 2, 3, 4도 유사한 구조로 추가 -->
    </div>
    
    <!-- impress.js 스크립트 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/impress.js/2.0.0/impress.min.js"></script>
    <script>
        impress().init();
    </script>
</body>
</html>