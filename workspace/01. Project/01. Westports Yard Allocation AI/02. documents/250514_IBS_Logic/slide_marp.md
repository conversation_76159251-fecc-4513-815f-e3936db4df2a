---
marp: true
theme: default
paginate: true
style: |
  /* 전체 스타일 */
  section {
    font-family: 'Open Sans', sans-serif;
    color: #374151;
    padding: 20px;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Roboto', sans-serif;
    color: #004488;
  }
  h1 {
    font-size: 1.8rem;
    color: white;
    background: linear-gradient(135deg, #0077cc 0%, #004488 100%);
    padding: 15px 25px;
    border-radius: 8px 8px 0 0;
    margin-top: 0;
  }
  /* 단계별 색상 */
  .stage-1-color { color: #2563eb; }
  .stage-2-color { color: #059669; }
  .stage-3-color { color: #7c3aed; }
  .stage-4-color { color: #dc2626; }
  /* 내용 상자 스타일 */
  .info-box {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  }
  /* 콘셉트 시각화 */
  .conceptual-visualization {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 10px;
    background-color: #f8fafc;
    border-radius: 8px;
    margin: 15px 0;
    border: 1px solid #d1dce5;
    text-align: center;
  }
  /* 그리드 레이아웃 */
  .grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin: 15px 0;
  }
  /* 단계별 헤더 색상 */
  .header-stage-1 { background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%); }
  .header-stage-2 { background: linear-gradient(135deg, #10b981 0%, #047857 100%); }
  .header-stage-3 { background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%); }
  .header-stage-4 { background: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%); }
---

<!-- 슬라이드 1: 인트로 -->
# IBS Core Computational Logic
## A 4-Stage Pipeline for Optimal Rehandling Suggestions

Driving terminal efficiency through intelligent, automated decision support.

![bg right:30% 80%](https://via.placeholder.com/400x800/ffffff/ffffff)

<div class="conceptual-visualization">
  <div>📥 Stage 1:<br>Candidate Identification</div>
  ➡️
  <div>🗺️ Stage 2:<br>Destination Selection</div>
  ➡️
  <div>✅ Stage 3:<br>Candidate Scoring</div>
  ➡️
  <div>📋 Stage 4:<br>Job Selection</div>
</div>

<div class="grid-container">
  <div class="info-box">
    <h3>📥 Stage 1: Candidate Identification</h3>
    <p>Identifies containers that could benefit from rehandling, applies filtering rules, and assigns initial priority scores.</p>
  </div>
  <div class="info-box">
    <h3>🗺️ Stage 2: Destination Selection</h3>
    <p>Evaluates potential destinations for each candidate and selects the optimal location based on multiple criteria.</p>
  </div>
  <div class="info-box">
    <h3>✅ Stage 3: Candidate Scoring</h3>
    <p>Calculates final scores for each candidate based on urgency and destination suitability, creating a ranked list.</p>
  </div>
  <div class="info-box">
    <h3>📋 Stage 4: Job Selection</h3>
    <p>Determines the number of jobs to suggest based on RTG capacity and selects the highest-scoring candidates.</p>
  </div>
</div>

**🔄 Executed periodically (configurable cycles) to ensure continuous optimization**

---

<!-- 슬라이드 2: 단계 1 -->
# <span style="display:inline-block;background-color:rgba(255,255,255,0.2);width:32px;height:32px;line-height:32px;text-align:center;border-radius:50%;margin-right:10px;font-size:0.8em;">1</span> Stage 1: Candidate Identification & Filtering
<span class="header-stage-1"></span>

<div style="text-align:center">
  <span style="font-size:2em" class="stage-1-color">📥</span>
  <h3 class="stage-1-color">Objective: Identify potential rehandles, filter ineligibles, and assign initial priority.</h3>
</div>

<div class="conceptual-visualization">
  <div>📦 All Containers</div>
  ➡️
  <div>🔍 Identify Triggers</div>
  ➡️
  <div>⭐ Prioritize</div>
  ➡️
  <div>✓✓ Filtered Candidates</div>
</div>

<div class="grid-container">
  <div class="info-box">
    <h3>⚠️ Identification Triggers</h3>
    <ul>
      <li><b>Dig-out Prevention:</b> Upper containers blocking earlier-departing lower ones. The upper container is the candidate.</li>
      <li><b>Yard Space Optimization:</b> Single-stacked containers potentially moved to improve layout for same-group units.</li>
      <li><b>Vessel/Voyage Grouping:</b> Top-stacked containers moved to consolidate with their group in the same bay.</li>
    </ul>
  </div>
  <div class="info-box">
    <h3>⬇️ Initial Prioritization</h3>
    <ul>
      <li><b>Dig-outs:</b> Scored by urgency (e.g., departure imminence).</li>
      <li><b>Slot Clearing:</b> Dynamically scored based on grouping benefits.</li>
      <li><b>Vessel Grouping:</b> Lowest default priority.</li>
    </ul>
  </div>
</div>

<div class="info-box">
  <h3>🚫 Exclusion Rules</h3>
  <p>Containers in <em>active jobs</em> or <em>recently moved</em> (within a configurable time window) are excluded.</p>
</div>

<p style="text-align:center;color:#2563eb;background-color:rgba(37,99,235,0.1);padding:8px;border-radius:6px;border-left:4px solid #2563eb;margin-top:10px;">
  <b>Output: A refined list of prioritized candidate containers.</b>
</p>

---

<!-- 슬라이드 3: 단계 2 -->
# <span style="display:inline-block;background-color:rgba(255,255,255,0.2);width:32px;height:32px;line-height:32px;text-align:center;border-radius:50%;margin-right:10px;font-size:0.8em;">2</span> Stage 2: Optimal Destination Selection
<span class="header-stage-2"></span>

<div style="text-align:center">
  <span style="font-size:2em" class="stage-2-color">🗺️</span>
  <h3 class="stage-2-color">Objective: For each candidate, evaluate potential destinations and select the single best one.</h3>
</div>

<div class="conceptual-visualization">
  <div>🚩 Candidate</div>
  ➡️
  <div>🔍 Find Spots</div>
  ➡️
  <div>✓ Filter & Score</div>
  ➡️
  <div>📍 Best Spot</div>
</div>

<div class="grid-container">
  <div class="info-box">
    <h3>📐 Destination Search & Filtering</h3>
    <ul>
      <li><b>Search Space:</b> Generate locations respecting operational constraints (e.g., travel distance).</li>
      <li><b>Feasibility Check:</b> Eliminate unsuitable spots using <em>Filter Factors</em> (e.g., max stack height, restricted zones).</li>
    </ul>
  </div>
  <div class="info-box">
    <h3>🏅 Location Scoring & Selection</h3>
    <ul>
      <li><b>Evaluation:</b> Score valid spots using weighted <em>Score Factors</em>. Key factors include:
        <ul>
          <li>Bay/Row Distance</li>
          <li>Destination Blocking</li>
          <li>Stack Stability</li>
          <li>Attribute Similarity</li>
        </ul>
      </li>
      <li><b>Best Fit:</b> Choose the highest-scoring destination. Priority on avoiding future rehandles.</li>
    </ul>
  </div>
</div>

<p style="text-align:center;color:#059669;background-color:rgba(5,150,105,0.1);padding:8px;border-radius:6px;border-left:4px solid #059669;margin-top:10px;">
  <b>Output: List of (Candidate, Optimal Destination, Destination Score).</b>
</p>

---

<!-- 슬라이드 4: 단계 3 -->
# <span style="display:inline-block;background-color:rgba(255,255,255,0.2);width:32px;height:32px;line-height:32px;text-align:center;border-radius:50%;margin-right:10px;font-size:0.8em;">3</span> Stage 3: Candidate Scoring
<span class="header-stage-3"></span>

<div style="text-align:center">
  <span style="font-size:2em" class="stage-3-color">✅</span>
  <h3 class="stage-3-color">Objective: Calculate a final score for each candidate (with its chosen destination) based on urgency and suitability.</h3>
</div>

<div class="info-box" style="max-width:700px;margin:0 auto;">
  <h3>🧮 Scoring Process</h3>
  <ol>
    <li>
      <b>Calculate 'First Lift Time':</b>
      <p>Estimated time until an underlying container is needed by the Quay Crane (QC), considering move, rehandle times, and buffers.</p>
      <div style="text-align:center">⏱️</div>
    </li>
    <li>
      <b>Assess Urgency Score:</b>
      <p>Based on the container's 'Required Yard Departure Time'. More urgent departures yield higher scores.</p>
    </li>
    <li>
      <b>Apply Urgency Boost:</b>
      <p>If 'First Lift Time' is critically near (e.g., within 4 hours), the score is significantly boosted.</p>
      <div style="text-align:center">🚀</div>
    </li>
    <li>
      <b>Combine Scores for Final Candidate Score:</b>
      <p>The <em>Destination Score</em> (from Stage 2) is combined with this stage's <em>Urgency Score</em>. (Exact combination logic is a key design point).</p>
      <p style="text-align:center"><em>Final Score = f(Destination Score + Urgency Score)</em></p>
    </li>
  </ol>
</div>

<p style="text-align:center;color:#7c3aed;background-color:rgba(124,58,237,0.1);padding:8px;border-radius:6px;border-left:4px solid #7c3aed;margin-top:10px;">
  <b>Output: Ranked list of (Candidate, Optimal Destination, Final Score).</b>
</p>

---

<!-- 슬라이드 5: 단계 4 -->
# <span style="display:inline-block;background-color:rgba(255,255,255,0.2);width:32px;height:32px;line-height:32px;text-align:center;border-radius:50%;margin-right:10px;font-size:0.8em;">4</span> Stage 4: Final Job Selection (RTG Aware)
<span class="header-stage-4"></span>

<div style="text-align:center">
  <span style="font-size:2em" class="stage-4-color">📋</span>
  <h3 class="stage-4-color">Objective: Determine the number of jobs to suggest based on RTG capacity and select top-scoring candidates.</h3>
</div>

<div class="conceptual-visualization">
  <div>⬆️ Ranked Candidates</div>
  ➡️
  <div>🔋 RTG Capacity Check</div>
  ➡️
  <div>✅ Select Top Jobs</div>
  ➡️
  <div>📤 Format for TOS</div>
</div>

<div class="grid-container">
  <div class="info-box">
    <h3>⏱️ RTG Capacity Estimation</h3>
    <p>The system determines available RTG capacity per cycle (via RTGCapacityCalculator module).</p>
    <p style="font-size:0.75em;font-style:italic;">Capacity ≈ (Planned Departures × Ops Time × Factor) / Avg. IBS Job Time. (Requires validation).</p>
    <p style="font-size:0.75em;">Settings: avg. IBS job time, departure horizon, RTG availability, min/max capacity.</p>
  </div>
  <div class="info-box">
    <h3>📋 Job Selection & Formatting</h3>
    <ul>
      <li><b>Prioritization:</b> Select highest-scoring jobs from Stage 3.</li>
      <li><b>Throttling:</b> Respects RTG capacity and per-bay job limits.</li>
      <li><b>Formatting:</b> Prepare jobs in TOS-compatible format (e.g., JSON).</li>
    </ul>
  </div>
</div>

<p style="text-align:center;color:#dc2626;background-color:rgba(220,38,38,0.1);padding:8px;border-radius:6px;border-left:4px solid #dc2626;margin-top:10px;">
  <b>Output: List of `job_plans` ready for TOS submission.</b>
</p>