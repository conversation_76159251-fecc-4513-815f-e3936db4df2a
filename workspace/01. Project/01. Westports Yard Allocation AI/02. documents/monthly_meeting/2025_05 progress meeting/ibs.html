<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IBS Core Computational Logic - Reveal.js Presentation</title>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reset.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reveal.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/theme/white.css" id="theme">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">

    <style>
        /* Ensure custom fonts are applied if theme doesn't override as desired */
        .reveal {
            font-family: 'Open Sans', sans-serif;
            color: #374151;
            font-size: 0.9em; /* Slightly reduce base font size */
            overflow: visible !important; /* Ensure content doesn't get cut off */
        }
        .reveal .slides {
            text-align: left; /* Align slides content to the left */
            height: auto !important; /* Allow slides to expand as needed */
            overflow: visible !important; /* Ensure content is visible */
            width: 100% !important; /* Use full width */
            max-width: 100% !important; /* Ensure max width is 100% */
        }
        .reveal .slides section {
            height: auto !important; /* Allow sections to expand as needed */
            min-height: 100%; /* Ensure sections take at least full height */
            padding: 10px 20px; /* Add some padding on sides */
            overflow: visible !important; /* Ensure content is visible */
            width: 100% !important; /* Use full width */
            max-width: 100% !important; /* Ensure max width is 100% */
        }
        .reveal h1, .reveal h2, .reveal h3, .reveal h4, .reveal h5, .reveal h6 {
            font-family: 'Roboto', sans-serif;
            color: #004488; /* Default heading color */
        }

        /* Retain and adapt custom styling for content blocks */
        .slide-header-custom { /* Custom class if Reveal.js default section header is not enough */
            padding: 12px 25px; 
            background: linear-gradient(135deg, #0077cc 0%, #004488 100%); 
            color: white;
            text-align: center;
            border-radius: 8px 8px 0 0; /* Example rounding */
            margin-bottom: 10px; /* Space below header */
            width: 100% !important; /* Use full width */
        }
        .slide-header-custom h1, .slide-header-custom h2 {
            font-size: 1.8rem;
            color: white;
            margin: 0; /* Reset margin if inside this custom header */
        }
        .slide-header-custom p.subtitle {
            font-size: 1rem; 
            margin-top: 8px; 
            opacity: 0.9;
            color: white;
        }

        .slide-content-custom { /* Custom class for main content area within a slide */
            padding: 15px;
            font-size: 0.95rem; /* Slightly smaller base font size for content */
            line-height: 1.6;
            overflow: visible !important; /* Ensure content doesn't get cut off */
            height: auto !important; /* Allow content to expand as needed */
        }
         .slide-content-custom.align-top {
             /* Reveal.js sections are flex containers, align-items: flex-start can be used or just structure normally */
        }

        .section-title-custom { /* For titles within content area */
            font-family: 'Roboto', sans-serif;
            font-size: 1.2rem; 
            font-weight: 600;
            color: #004488; 
            margin-top: 12px;
            margin-bottom: 1rem; 
            text-align: center;
        }
        
        /* List styling (general) - theme might provide some */
        .reveal ul, .reveal ol {
            margin-left: 1.5em; /* Standard indentation */
        }
        .reveal ul li, .reveal ol li {
            margin-bottom: 0.6rem;
        }

        /* Info Box Styling (adapted for Reveal.js context) */
        .info-box { /* For key information blocks */
            background-color: #f8fafc; 
            border: 1px solid #e2e8f0; 
            border-radius: 8px;
            padding: 15px; 
            margin-bottom: 10px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: box-shadow 0.3s ease, transform 0.3s ease;
        }
        
        .info-box:hover {
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }

        .info-box h4 {
            font-family: 'Roboto', sans-serif;
            font-size: 1.1rem; 
            font-weight: 600;
            color: #0055a4; 
            margin-top: 0; /* Reset top margin for h4 in info-box */
            margin-bottom: 10px; /* Space below heading */
            display: flex;
            align-items: center;
            gap: 8px; /* Space between icon and text */
            padding-bottom: 6px;
            border-bottom: 2px solid rgba(0,85,164,0.2);
        }
        
        /* Stage-specific info box header colors */
        .stage-1-color .info-box h4 { color: #2563eb; border-bottom-color: rgba(37,99,235,0.2); }
        .stage-2-color .info-box h4 { color: #059669; border-bottom-color: rgba(5,150,105,0.2); }
        .stage-3-color .info-box h4 { color: #7c3aed; border-bottom-color: rgba(124,58,237,0.2); }
        .stage-4-color .info-box h4 { color: #dc2626; border-bottom-color: rgba(220,38,38,0.2); }

        .info-box h4 i { 
            margin-right: 10px; 
            color: #0077cc;
            font-size: 1.1em; /* Adjust icon size relative to h4 */
        }
        .info-box ul {
            list-style: none; 
            padding-left: 0; 
            margin-top: 0.5rem;
        }
        .info-box ul li { 
            display: flex; 
            align-items: flex-start; 
            margin-bottom: 0.6rem; 
            padding-left: 0; 
            line-height: 1.5; 
        }
        .info-box ul li:last-child {
            margin-bottom: 0; 
        }
        .info-box ul li i.fa-fw { 
            margin-right: 0.6rem; 
            margin-top: 0.15em; 
            color: #0077cc; 
            width: 1.2em; 
            font-size: 0.95em; /* Slightly smaller icons in lists */
        }
        .info-box ul li div { 
            flex: 1; 
        }
        .info-box ul li strong { 
            font-weight: 600;
            color: #374151; 
            display: inline;
        }
        .info-box ul li p.description, .info-box p.description { 
            font-size: 0.9rem; 
            color: #4b5563; 
            line-height: 1.55; 
            margin-top: 0.1rem; 
            display: block; 
        }
        .info-box p:not(.description) { /* For general paragraphs inside info-box */
             font-size: 0.95rem; color: #4b5563; line-height: 1.6;
        }

        .icon-group { display: flex; justify-content: center; align-items: center; gap: 25px; margin: 15px 0; }
        .icon-group .icon-item { display: flex; flex-direction: column; align-items: center; }
        .icon-group .icon-item i {
            font-size: 2.5rem; 
            margin-bottom: 8px;
            color: #0077cc;
        }
        .icon-group .icon-item span { font-size: 0.8rem; color: #555; }

        /* Stage-specific styling for improved visual hierarchy */
        .stage-icon-custom { /* Renamed to avoid conflict if theme has .stage-icon */
            font-size: 2.8rem; 
            margin-bottom: 1rem; 
            display: block; /* Ensure it takes space */
        }
        
        /* Color coding for each stage */
        .stage-1-color { color: #2563eb; } /* Blue */
        .stage-2-color { color: #059669; } /* Green */
        .stage-3-color { color: #7c3aed; } /* Purple */
        .stage-4-color { color: #dc2626; } /* Red */
        .conclusion-color { color: #28a745; } /* Success Green */

        .flow-arrow { /* For arrows in process flows */
            color: #94a3b8; 
            font-size: 1.2rem;
            position: relative;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 0.7; }
            50% { opacity: 1; }
            100% { opacity: 0.7; }
        }

        .conceptual-visualization { /* For process flow diagrams */
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 10px;
            background-color: #f8fafc;
            border-radius: 8px;
            margin: 15px 0; 
            border: 1px solid #d1dce5; 
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .conceptual-visualization .step { 
            text-align: center; 
            padding: 8px 12px; 
            background-color: white;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        } 
        .conceptual-visualization .step:hover {
            transform: translateY(-3px);
        }
        .conceptual-visualization .step i { 
            font-size: 2rem; 
            margin-bottom: 5px; 
            display: block; 
        }
        .conceptual-visualization .step span { 
            font-size: 0.85rem; 
            color: #374151; 
            display: block; 
            font-weight: 500;
        }

        .code-like {
            background-color: #2d3748; 
            color: #e2e8f0; 
            padding: 8px 12px;
            border-radius: 5px;
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.8rem;
            white-space: pre-wrap;
            margin: 6px 0; /* Added vertical margin */
            display: block;
            border: 1px solid #4a5568; 
            text-align: left; /* Ensure code is left-aligned */
        }

        /* Tailwind Grid utilities might need to be re-applied if not using Tailwind's CSS directly */
        /* Or use Reveal.js's r-stack or r-fit-text for layout if preferred */
        .grid-container-custom { /* For multi-column layouts */
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Wider columns */
            gap: 15px;
            width: 100%;
            margin-top: 12px;
        }
         .grid-container-custom .info-box.md-col-span-full { /* Basic span full for 2-col layout */
            grid-column: 1 / -1;
            margin-top: 15px;
        }

        .slide-footer-custom { /* Custom class for a footer bar within a slide */
            padding: 10px 30px; 
            background-color: #f0f0f0; /* Light gray footer */
            color: #555;
            text-align: center;
            font-size: 0.8rem; 
            border-top: 1px solid #ddd;
            margin-top: auto; /* Pushes to bottom if section is flex container */
        }
    </style>
</head>
<body>

    <div class="reveal">
        <div class="slides">
            <section>
                <div class="slide-header-custom">
                    <h1>IBS Core Computational Logic</h1>
                    <p class="subtitle">A 4-Stage Pipeline for Optimal Rehandling Suggestions</p>
                </div>
                <div class="slide-content-custom text-center">
                    <p class="text-xl mb-4" style="font-size: 1.2em; margin-bottom: 1rem;">Driving terminal efficiency through intelligent, automated decision support.</p>
                    
                    <div class="conceptual-visualization my-4 mx-auto max-w-3xl !py-4" style="max-width:800px;"> 
                        <div class="step"><i class="fas fa-filter stage-1-color"></i><span>Stage 1:<br>Candidate Identification</span></div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"><i class="fas fa-map-marked-alt stage-2-color"></i><span>Stage 2:<br>Destination Selection</span></div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"><i class="fas fa-clipboard-check stage-3-color"></i><span>Stage 3:<br>Candidate Scoring</span></div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"><i class="fas fa-tasks stage-4-color"></i><span>Stage 4:<br>Job Selection</span></div>
                    </div>

                    <div class="grid-container-custom" style="grid-template-columns: repeat(2, 1fr); gap: 15px; margin-top:1.5rem; margin-bottom:1rem;">
                        <div class="info-box">
                            <h4><i class="fas fa-filter stage-1-color"></i>Stage 1: Candidate Identification</h4>
                            <p class="description">Identifies containers that could benefit from rehandling, applies filtering rules, and assigns initial priority scores.</p>
                        </div>
                        <div class="info-box">
                            <h4><i class="fas fa-map-marked-alt stage-2-color"></i>Stage 2: Destination Selection</h4>
                            <p class="description">Evaluates potential destinations for each candidate and selects the optimal location based on multiple criteria.</p>
                        </div>
                        <div class="info-box">
                            <h4><i class="fas fa-clipboard-check stage-3-color"></i>Stage 3: Candidate Scoring</h4>
                            <p class="description">Calculates final scores for each candidate based on urgency and destination suitability, creating a ranked list.</p>
                        </div>
                        <div class="info-box">
                            <h4><i class="fas fa-tasks stage-4-color"></i>Stage 4: Job Selection</h4>
                            <p class="description">Determines the number of jobs to suggest based on RTG capacity and selects the highest-scoring candidates.</p>
                        </div>
                    </div>

                    <p class="text-center font-semibold pt-2" style="margin-top:0.7rem; background-color: rgba(59, 130, 246, 0.1); padding: 8px; border-radius: 6px; border-left: 4px solid #3b82f6;">
                        <i class="fas fa-sync-alt mr-2"></i>Executed periodically (configurable cycles) to ensure continuous optimization
                    </p>
                </div>
                <aside class="notes">Speaker notes for slide 1.</aside> </section>

            <section>
                <div class="slide-header-custom" style="background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);">
                    <h1><span class="stage-number" style="display: inline-block; background-color: rgba(255,255,255,0.2); width: 32px; height: 32px; line-height: 32px; border-radius: 50%; margin-right: 10px; font-size: 0.8em;">1</span>Stage 1: Candidate Identification & Filtering</h1>
                </div>
                <div class="slide-content-custom align-top">
                    <div class="text-center mb-4">
                        <i class="fas fa-filter stage-icon-custom stage-1-color"></i>
                        <h3 class="section-title-custom !mb-2" style="color: #2563eb;">Objective: Identify potential rehandles, filter ineligibles, and assign initial priority.</h3>
                    </div>

                    <div class="conceptual-visualization mb-3"> 
                        <div class="step"> <i class="fas fa-cubes stage-1-color"></i> <span>All Containers</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-search-plus stage-1-color"></i> <span>Identify Triggers</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-star-half-alt stage-1-color"></i> <span>Prioritize</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-check-double stage-1-color"></i> <span>Filtered Candidates</span> </div>
                    </div>
                    
                    <div class="grid-container-custom" style="grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="info-box">
                            <h4><i class="fas fa-exclamation-triangle"></i>Identification Triggers</h4>
                            <ul>
                                <li><i class="fas fa-layer-group fa-fw"></i><div><strong>Dig-out Prevention:</strong><p class="description">Upper containers blocking earlier-departing lower ones. The upper container is the candidate.</p></div></li>
                                <li><i class="fas fa-th-large fa-fw"></i><div><strong>Yard Space Optimization:</strong><p class="description">Single-stacked containers potentially moved to improve layout for same-group units.</p></div></li>
                                <li><i class="fas fa-ship fa-fw"></i><div><strong>Vessel/Voyage Grouping:</strong><p class="description">Top-stacked containers moved to consolidate with their group in the same bay.</p></div></li>
                            </ul>
                        </div>
                        <div class="info-box">
                            <h4><i class="fas fa-sort-amount-down"></i>Initial Prioritization</h4>
                            <ul>
                                <li><i class="fas fa-tachometer-alt fa-fw"></i><div><strong>Dig-outs:</strong><p class="description">Scored by urgency (e.g., departure imminence).</p></div></li>
                                <li><i class="fas fa-calculator fa-fw"></i><div><strong>Slot Clearing:</strong><p class="description">Dynamically scored based on grouping benefits.</p></div></li>
                                <li><i class="fas fa-angle-double-down fa-fw"></i><div><strong>Vessel Grouping:</strong><p class="description">Lowest default priority.</p></div></li>
                            </ul>
                        </div>
                    </div>
                    <div class="info-box" style="margin-top: 15px;"> 
                        <h4><i class="fas fa-ban stage-1-color"></i>Exclusion Rules</h4>
                        <p class="description" style="display:flex; align-items:center;"><i class="fas fa-tasks mr-2 text-sm stage-1-color"></i>Containers in <em>active jobs</em> or <em>recently moved</em> (within a configurable time window) are excluded.</p>
                    </div>
                     <p class="text-center font-semibold pt-1" style="margin-top:0.5rem; color: #2563eb; background-color: rgba(37, 99, 235, 0.1); padding: 8px; border-radius: 6px; border-left: 4px solid #2563eb;">Output: A refined list of prioritized candidate containers.</p> 
                </div>
            </section>

            <section>
                <div class="slide-header-custom" style="background: linear-gradient(135deg, #10b981 0%, #047857 100%);">
                    <h1><span class="stage-number" style="display: inline-block; background-color: rgba(255,255,255,0.2); width: 32px; height: 32px; line-height: 32px; border-radius: 50%; margin-right: 10px; font-size: 0.8em;">2</span>Stage 2: Optimal Destination Selection</h1>
                </div>
                <div class="slide-content-custom align-top">
                    <div class="text-center mb-3">
                        <i class="fas fa-map-marked-alt stage-icon-custom stage-2-color"></i>
                        <h3 class="section-title-custom !mb-2" style="color: #059669;">Objective: For each candidate, evaluate potential destinations and select the single best one.</h3>
                    </div>

                    <div class="conceptual-visualization mb-3">
                        <div class="step"> <i class="fas fa-map-signs stage-2-color"></i> <span>Candidate</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-search-location stage-2-color"></i> <span>Find Spots</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-check-circle stage-2-color"></i> <span>Filter & Score</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-map-marker-check stage-2-color"></i> <span>Best Spot</span> </div>
                    </div>

                    <div class="grid-container-custom" style="grid-template-columns: 1fr 1fr; gap: 20px;"> 
                        <div class="info-box">
                            <h4><i class="fas fa-drafting-compass"></i>Destination Search & Filtering</h4>
                             <ul>
                                <li><i class="fas fa-route fa-fw"></i><div><strong>Search Space:</strong><p class="description">Generate locations respecting operational constraints (e.g., travel distance).</p></div></li>
                                <li><i class="fas fa-compress-arrows-alt fa-fw"></i><div><strong>Feasibility Check:</strong><p class="description">Eliminate unsuitable spots using <em>Filter Factors</em> (e.g., max stack height, restricted zones).</p></div></li>
                            </ul>
                        </div>
                        <div class="info-box">
                            <h4><i class="fas fa-medal"></i>Location Scoring & Selection</h4>
                            <ul>
                                <li><i class="fas fa-clipboard-list fa-fw"></i><div><strong>Evaluation:</strong><p class="description">Score valid spots using weighted <em>Score Factors</em>. Key factors include:</p>
                                    <ul class="mt-1 ml-0" style="font-size:0.85em;"> 
                                        <li><i class="fas fa-directions fa-fw text-sm"></i><div>Bay/Row Distance</div></li>
                                        <li><i class="fas fa-ban fa-fw text-sm"></i><div>Destination Blocking</div></li>
                                        <li><i class="fas fa-balance-scale-left fa-fw text-sm"></i><div>Stack Stability</div></li>
                                        <li><i class="fas fa-object-group fa-fw text-sm"></i><div>Attribute Similarity</div></li>
                                    </ul>
                                </div></li>
                                <li><i class="fas fa-trophy fa-fw"></i><div><strong>Best Fit:</strong><p class="description">Choose the highest-scoring destination. Priority on avoiding future rehandles.</p></div></li>
                            </ul>
                        </div>
                    </div>
                     <p class="text-center font-semibold pt-2" style="margin-top:0.7rem; color: #059669; background-color: rgba(5, 150, 105, 0.1); padding: 8px; border-radius: 6px; border-left: 4px solid #059669;">Output: List of (Candidate, Optimal Destination, Destination Score).</p>
                </div>
            </section>

            <section>
                <div class="slide-header-custom" style="background: linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%);">
                    <h1><span class="stage-number" style="display: inline-block; background-color: rgba(255,255,255,0.2); width: 32px; height: 32px; line-height: 32px; border-radius: 50%; margin-right: 10px; font-size: 0.8em;">3</span>Stage 3: Candidate Scoring</h1>
                </div>
                <div class="slide-content-custom align-top">
                    <div class="text-center mb-3">
                        <i class="fas fa-clipboard-check stage-icon-custom stage-3-color"></i>
                        <h3 class="section-title-custom !mb-2" style="color: #7c3aed;">Objective: Calculate a final score for each candidate (with its chosen destination) based on urgency and suitability.</h3>
                    </div>
                    <div class="info-box max-w-3xl mx-auto" style="max-width: 700px; margin-left:auto; margin-right:auto;"> 
                        <h4><i class="fas fa-calculator"></i>Scoring Process</h4>
                        <ol class="list-decimal text-gray-700 mt-2" style="padding-left: 1.5em; font-size:0.95em;">
                            <li class="mb-2"> 
                                <strong>Calculate 'First Lift Time':</strong>
                                <p class="description ml-4 mt-1" style="font-size:0.85em;">Estimated time until an underlying container is needed by the Quay Crane (QC), considering move, rehandle times, and buffers.</p>
                                <div class="text-center my-1"><i class="fas fa-stopwatch text-blue-500 text-xl"></i></div>
                            </li>
                            <li class="mb-2">
                                <strong>Assess Urgency Score:</strong>
                                <p class="description ml-4 mt-1" style="font-size:0.85em;">Based on the container's 'Required Yard Departure Time'. More urgent departures yield higher scores.</p>
                            </li>
                            <li class="mb-2">
                                <strong>Apply Urgency Boost:</strong>
                                <p class="description ml-4 mt-1" style="font-size:0.85em;">If 'First Lift Time' is critically near (e.g., within 4 hours), the score is significantly boosted.</p>
                                <div class="text-center my-1"><i class="fas fa-rocket text-red-500 text-xl"></i></div>
                            </li>
                            <li>
                                <strong>Combine Scores for Final Candidate Score:</strong>
                                <p class="description ml-4 mt-1" style="font-size:0.85em;">The <em>Destination Score</em> (from Stage 2) is combined with this stage's <em>Urgency Score</em>. (Exact combination logic is a key design point).</p>
                                <p class="text-center mt-1 font-medium" style="font-size:0.85em;"><em>Final Score = f(Destination Score + Urgency Score)</em></p>
                            </li>
                        </ol>
                    </div>
                     <p class="text-center font-semibold pt-2" style="margin-top:0.7rem; color: #7c3aed; background-color: rgba(124, 58, 237, 0.1); padding: 8px; border-radius: 6px; border-left: 4px solid #7c3aed;">Output: Ranked list of (Candidate, Optimal Destination, Final Score).</p>
                </div>
            </section>

            <section>
                <div class="slide-header-custom" style="background: linear-gradient(135deg, #ef4444 0%, #b91c1c 100%);">
                    <h1><span class="stage-number" style="display: inline-block; background-color: rgba(255,255,255,0.2); width: 32px; height: 32px; line-height: 32px; border-radius: 50%; margin-right: 10px; font-size: 0.8em;">4</span>Stage 4: Final Job Selection (RTG Aware)</h1>
                </div>
                <div class="slide-content-custom align-top">
                    <div class="text-center mb-3">
                        <i class="fas fa-tasks stage-icon-custom stage-4-color"></i>
                        <h3 class="section-title-custom !mb-2" style="color: #dc2626;">Objective: Determine the number of jobs to suggest based on RTG capacity and select top-scoring candidates.</h3>
                    </div>

                    <div class="conceptual-visualization mb-3">
                        <div class="step"> <i class="fas fa-sort-amount-up-alt stage-4-color"></i> <span>Ranked Candidates</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-charging-station stage-4-color"></i> <span>RTG Capacity Check</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-check-square stage-4-color"></i> <span>Select Top Jobs</span> </div>
                        <i class="fas fa-arrow-right flow-arrow"></i>
                        <div class="step"> <i class="fas fa-file-export stage-4-color"></i> <span>Format for TOS</span> </div>
                    </div>

                    <div class="grid-container-custom" style="grid-template-columns: 1fr 1fr; gap: 20px;"> 
                        <div class="info-box">
                            <h4><i class="fas fa-tachometer-alt"></i>RTG Capacity Estimation</h4>
                            <p class="description">The system determines available RTG capacity per cycle (via RTGCapacityCalculator module).</p>
                            <p class="text-xs mt-1 italic text-gray-500" style="font-size:0.75em;">Capacity &approx; (Planned Departures &times; Ops Time &times; Factor) / Avg. IBS Job Time. (Requires validation).</p>
                            <p class="text-xs mt-1" style="font-size:0.75em;">Settings: avg. IBS job time, departure horizon, RTG availability, min/max capacity.</p>
                        </div>
                        <div class="info-box">
                            <h4><i class="fas fa-clipboard-list"></i>Job Selection & Formatting</h4>
                            <ul>
                                <li><i class="fas fa-filter fa-fw"></i><div><strong>Prioritization:</strong><p class="description">Select highest-scoring jobs from Stage 3.</p></div></li>
                                <li><i class="fas fa-compress fa-fw"></i><div><strong>Throttling:</strong><p class="description">Respects RTG capacity and per-bay job limits.</p></div></li>
                                <li><i class="fas fa-cogs fa-fw"></i><div><strong>Formatting:</strong><p class="description">Prepare jobs in TOS-compatible format (e.g., JSON).</p></div></li>
                            </ul>
                        </div>
                    </div>
                     <p class="text-center font-semibold pt-2" style="margin-top:0.7rem; color: #dc2626; background-color: rgba(220, 38, 38, 0.1); padding: 8px; border-radius: 6px; border-left: 4px solid #dc2626;">Output: List of <code>job_plans</code> ready for TOS submission.</p>
                </div>
            </section>


        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reveal.min.js"></script>
    <script>
        Reveal.initialize({
            controls: true,       // Display controls in the bottom right corner
            progress: true,       // Display a presentation progress bar
            history: true,        // Push each slide change to the browser history
            center: false,        // Disable vertical centering of slides to prevent content cutoff
            hash: true,           // Add a hash to the URL of each slide
            slideNumber: 'c/t',   // Display current slide number / total slides
            transition: 'slide',  // Default transition
            backgroundTransition: 'fade', // Transition for backgrounds
            width: '100%',        // Use full width of browser
            height: '100%',       // Use full height of browser
            margin: 0.05,         // Minimal margin
            minScale: 0.2,        // Allow more scaling down
            maxScale: 1.5,        // Limit maximum scale
            display: 'block',     // Use block display
            disableLayout: false  // Enable layout
        });
    </script>

</body>
</html>