<!DOCTYPE html>
<html lang="ko">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Westports AI Yard Allocation POC</title>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reset.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reveal.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/theme/white.min.css" id="theme">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600&display=swap" rel="stylesheet">

<style>
:root {
  --font-body: 'Open Sans', sans-serif;
  --font-heading: 'Roboto', sans-serif;
  --primary: #004488; /* Dark blue */
  --secondary: #007bff; /* Lighter blue for accents if needed */
  --text-light: #f8f9fa;
  --text-dark: #333333;
  --slide-bg: #ffffff;

  --stage1: #3b82f6; --stage1-dark: #1e40af; /* Blue */
  --stage2: #10b981; --stage2-dark: #047857; /* Green */
  --stage3: #8b5cf6; --stage3-dark: #6d28d9; /* Purple */
  --stage4: #ef4444; --stage4-dark: #b91c1c; /* Red */
}

body {
  font-family: var(--font-body);
  color: var(--text-dark);
}

.reveal .slides {
  text-align: left;
}

.reveal .slides section {
  background-color: var(--slide-bg);
  padding: 25px 25px 25px 35px;
  box-sizing: border-box;
}

.reveal h1, .reveal h2, .reveal h3, .reveal h4, .reveal h5, .reveal h6 {
  font-family: var(--font-heading);
  color: var(--primary);
  margin-bottom: 20px;
  text-transform: none;
  font-weight: 500;
}

.reveal h1 { font-size: 2.2em; } /* General H1, not slide title in header */
.reveal h2 { font-size: 1.8em; } /* For sub-headings within content body */
.reveal p { margin-bottom: 10px; line-height: 1.5; }
.reveal ul, .reveal ol { margin-bottom: 12px; margin-left: 35px; }
.reveal li { margin-bottom: 6px; line-height: 1.4; }

.reveal strong { font-weight: 600; } /* Default strong is 600 in Roboto */
.reveal em { font-style: italic; }

.slide-header-custom {
  padding: 20px 25px;
  color: var(--text-light);
  margin: -25px -25px 20px -25px; /* Full bleed header */
  border-bottom: 3px solid rgba(0,0,0,0.05);
  border-radius: 8px 8px 0 0; /* Rounded top corners for stage slides */
  overflow: hidden;
}

.slide-header-custom h1 { /* Title in header for stage slides */
  color: var(--text-light);
  font-size: 1.4em;
  margin: 0;
  display: flex;
  align-items: center;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.slide-header-custom p.subtitle { /* Subtitle in cover header */
  font-size: 1.1em;
  font-weight: 300;
  opacity: 0.9;
  margin-top: 8px;
  margin-bottom: 0;
  color: var(--text-light);
}

.stage-number {
  display: inline-block;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  margin-right: 15px;
  font-size: 0.7em;
  font-weight: 700;
  flex-shrink: 0;
}

.slide-content-custom {
  padding-top: 10px;
  padding-left: 5px;
  height: calc(100% - 85px); /* Approximate, depends on actual header height */
  overflow-y: auto;
  box-sizing: border-box;
}
.slide-content-custom.align-top { text-align: left; }
.slide-content-custom.text-center { text-align: center; }

/* Cover Slide Specifics */
.reveal .slides > section:first-child .slide-header-custom {
  background-color: var(--primary);
  border-radius: 0; /* Cover is full bleed */
}
.reveal .slides > section:first-child .slide-header-custom h1 { /* Cover title */
  font-size: 2em;
  text-align: center;
  display: block;
  font-weight: 700;
  margin-top: 0;
  color: white;
}
.reveal .slides > section:first-child .slide-header-custom p.subtitle { /* Cover subtitle */
  font-size: 1.3em;
  text-align: center;
  margin-top: 10px;
}
.reveal .slides > section:first-child .slide-content-custom {
  padding-top: 20px;
  font-size: 1.0em;
}
.reveal .slides > section:first-child .slide-content-custom p {
  margin-bottom: 10px;
}

/* Nested Lists */
.reveal ul ul, .reveal ol ol, .reveal ul ol, .reveal ol ul {
  margin-left: 25px;
  margin-bottom: 4px;
  font-size: 0.9em;
  line-height: 1.3;
}

.reveal .slide-content-custom li strong,
.reveal .slide-content-custom p strong {
  /* color: var(--primary); Already handled by default strong + primary color for headers */
}

/* Conclusion Slide (using its own section, not stageTpl) */
.conclusion-slide-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 100%;
  padding: 30px;
  background: linear-gradient(135deg, var(--stage1) 0%, var(--stage1-dark) 100%);
  color: var(--text-light);
  box-sizing: border-box;
}
.conclusion-slide-container h1 {
  font-size: 2.4em;
  color: var(--text-light);
  margin-bottom: 25px;
}
.conclusion-slide-container p, .conclusion-slide-container li {
  font-size: 1.1em;
  line-height: 1.6;
  color: var(--text-light); /* Ensure text is light on dark bg */
}
.conclusion-slide-container ul {
  list-style-position: inside;
  padding-left: 0;
  text-align: left;
  display: inline-block; /* To allow centering of the UL block */
  margin-bottom: 20px;
}
.conclusion-slide-container ul li {
  margin-bottom: 10px;
}
.conclusion-slide-container ul ul { /* Nested list in conclusion */
    margin-left: 20px;
    font-size: 0.95em;
}
.conclusion-slide-container strong {
    font-weight: 600; /* Ensure strong stands out */
}

.stage-1-grad{background:linear-gradient(135deg,var(--stage1) 0%,var(--stage1-dark) 100%)}
.stage-2-grad{background:linear-gradient(135deg,var(--stage2) 0%,var(--stage2-dark) 100%)}
.stage-3-grad{background:linear-gradient(135deg,var(--stage3) 0%,var(--stage3-dark) 100%)}
.stage-4-grad{background:linear-gradient(135deg,var(--stage4) 0%,var(--stage4-dark) 100%)}
</style>
</head>
<body>
<div class="reveal"><div class="slides">
<section>
  <div class="slide-header-custom">
    <h1>Westports AI Yard Allocation POC</h1>
    <p class="subtitle">Progress Update & Response to Queries</p>
  </div>
  <div class="slide-content-custom text-center">
    <p style="font-size:0.9em; opacity:0.8;">May 14, 2025 (16:00 MYT / 17:00 KST)</p>
    <p style="font-size:1.1em; margin-top:20px;"><strong>CyberLogitec</strong></p>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-1-grad">
    <h1><span class="stage-number">1</span>Table of Contents</h1>
  </div>
  <div class="slide-content-custom align-top">
    <table style="width:100%; border-collapse: collapse; margin-top:10px;">
      <tr>
        <td style="width:30%; vertical-align:top; padding-right:10px;"><strong>Project Status (2-6)</strong></td>
        <td style="width:70%; vertical-align:top;">• Project Highlights • IBS Updates • Key Milestones<br>• CDC Resolution • Sync Delay Handling</td>
      </tr>
      <tr>
        <td style="width:30%; vertical-align:top; padding-top:8px; padding-right:10px;"><strong>Queries (7-15)</strong></td>
        <td style="width:70%; vertical-align:top; padding-top:8px;">• UAT Readiness (Q1) • Deployment (Q2) • Interface (Q3)<br>• Test Docs (Q4) • UAT Scope (Q5) • Production (Q6-Q8) • On-site (Q9)</td>
      </tr>
      <tr>
        <td style="width:30%; vertical-align:top; padding-top:8px; padding-right:10px;"><strong>Closing (16-17)</strong></td>
        <td style="width:70%; vertical-align:top; padding-top:8px;">• Schedule & SOW • Virtual Terminal Preview</td>
      </tr>
    </table>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-2-grad">
    <h1><span class="stage-number">2</span>Project Highlights & Key Milestones</h1>
  </div>
  <div class="slide-content-custom align-top">
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
      <div>
        <h3>Key Achievements</h3>
        <ul>
          <li><strong>88%</strong> project completion</li>
          <li>YA CDC issue <strong>resolved</strong> (May 14)</li>
          <li>IBS core <strong>85% developed</strong> (integration testing pending)</li>
        </ul>
        
        <h3>Launch Plan</h3>
        <ul>
          <li>Early June target (Go-Live: June 6)</li>
          <li>UAT: May 26-31</li>
          <li>Pre-Production: June 1-3</li>
          <li>Production Deployment (Single Block): June 3-5</li>
        </ul>
      </div>
      
      <div>
        <h3>Critical Dates</h3>
        <ul>
          <li>CLT Internal Testing: May 15-22</li>
          <li>Remote UAT by Westports: May 26-31</li>
          <li>Production Deployment (Single Block): June 3-5</li>
          <li>CLT On-site Support (Week 1): June 9-13</li>
          <li>CLT On-site Support (Week 2): June 16-20</li>
        </ul>
        
        <p style="margin-top: 15px;">
          <a href="250514_project_timeline_updated.html" target="_blank" style="color: var(--primary); font-weight: 600;">
            View Detailed Timeline →
          </a>
        </p>
      </div>
    </div>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-3-grad">
    <h1><span class="stage-number">3</span>Inter Block Shifting (IBS) - Key Updates</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Core Functionality:</strong> <strong>85% complete</strong>
    <ul>
<li>4-Stage Pipeline: Identification → Destination Selection → Scoring → Job Selection</li>
<li>Remaining 15%: Final integration testing with TOS</li>
</ul>
</li>
<li><strong>Current Status:</strong> Finalizing test modules for TOS integration</li>
<li><strong>Testing Schedule:</strong> Starting <strong>May 16th</strong>
    <ul>
<li>Covering functional, integration & performance testing</li>
</ul>
</li>
</ul>
<p style="margin-top:10px;"><em>Detailed documentation available upon request</em></p>
<p style="margin-top:15px;">
  <a href="ibs.html" target="_blank" style="color: var(--primary); font-weight: 600;">
    View IBS Detailed Technical Documentation →
  </a>
</p>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-1-grad">
    <h1><span class="stage-number">4</span>CDC Issue: Resolution & System Stability</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Problem:</strong> Data sync errors due to DB foreign key constraints</li>
<li><strong>Resolution:</strong> <strong>Fully resolved</strong> by removing specific constraints on destination DB</li>
<li><strong>Outcomes:</strong>
    <ul>
<li>Stable sync time: <strong>2-3 seconds</strong></li>
<li>System stability significantly enhanced</li>
</ul>
</li>
<li><strong>Ongoing:</strong> Continuous monitoring and data validation</li>
</ul>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-2-grad">
    <h1><span class="stage-number">5</span>CDC Sync Delay (2-3s): Operational Handling Strategy</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Context:</strong> 2-3s sync delay in dynamic yard operations</li>
<li><strong>Mitigation Strategy:</strong>
    <ol>
<li><strong>Pre-emptive Info:</strong> AI references latest TOS plans</li>
<li><strong>Multiple Options:</strong> AI provides ranked candidate list</li>
</ol>
</li>
<li><strong>TOS Validation:</strong>
    <ul>
<li>Final availability check before execution</li>
<li>Fallback options if needed</li>
</ul>
</li>
</ul>
<p style="margin-top:10px;"><strong>Benefit: Dual approach ensures reliability</strong></p>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-3-grad">
    <h1><span class="stage-number">6</span>UAT Readiness & On-Site Support Proposal (Q1)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Module Status:</strong> Core functions developed, now in testing phase</li>
<li><strong>UAT Environment:</strong> Setup to complete by May 22nd</li>
<li><strong>On-Site Support Proposal:</strong>
    <ul>
<li><strong>Duration:</strong> June 9-20 (2 weeks)</li>
<li><strong>Week 1 (June 9-13):</strong> On-site UAT support & issue resolution</li>
<li><strong>Week 2 (June 16-20):</strong> Production monitoring & stabilization</li>
</ul>
</li>
</ul>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-4-grad">
    <h1><span class="stage-number">7</span>TOS UAT Environment Deployment Schedule (Q2)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>YA AI & IBS Modules:</strong>
    <ul>
<li>UAT deployment by <strong>May 21st</strong></li>
<li>Stabilization before PROD in early June</li>
</ul>
</li>
<li><strong>Dashboard:</strong>
    <ul>
<li>Initial features: UAT by <strong>May 26th</strong></li>
<li>Full UI/UX review: <strong>May 30th</strong></li>
</ul>
</li>
</ul>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-3-grad">
    <h1><span class="stage-number">8</span>Interface Utilization Plan for POC (Q3)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Haulier & RTG GPS:</strong> <strong>Yes, actively used</strong>
    <ul>
<li>Enhances AI accuracy and responsiveness</li>
<li>Improves travel time estimation and slot recommendations</li>
</ul>
</li>
<li><strong>Pre-Arrival Booking:</strong> <strong>Yes, utilized</strong>
    <ul>
<li>Key input for proactive yard planning</li>
</ul>
</li>
<li><strong>Bonus Deliverable:</strong>
    <ul>
<li>Equipment tracking via OPUS DigiPort VT during on-site visit</li>
</ul>
</li>
</ul>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-2-grad">
    <h1><span class="stage-number">9</span>Test Result Documentation Schedule (Q4)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Phase 1: Internal Test Results:</strong>
    <ul>
<li>Delivery by <strong>May 21st</strong> with UAT deployment</li>
<li>Covers core functions and integration verification</li>
</ul>
</li>
<li><strong>Phase 2: UAT Progress Reports:</strong>
    <ul>
<li><strong>Weekly summaries</strong> throughout UAT period</li>
<li>Includes progress, issues, and stability metrics</li>
<li>Ad-hoc reports for critical milestones as needed</li>
</ul>
</li>
</ul>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-1-grad">
    <h1><span class="stage-number">10</span>UAT Scope Confirmation (Q5)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <p><strong>Westports' Proposed UAT Scope:</strong> Fully aligned</p>
<ul>
<li>AI YA logic for POC blocks</li>
<li>Existing YA logic for non-POC blocks</li>
<li>Fallback YA logic for POC blocks (BCP)</li>
<li>Auto IBS logic for POC blocks</li>
<li>Manual IBS logic for non-POC blocks</li>
<li>Fallback IBS logic for POC blocks (BCP)</li>
</ul>
<p style="margin-top:10px;"><strong>CLT fully supports this scope with comprehensive test cases</strong></p>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-4-grad">
    <h1><span class="stage-number">11</span>Production Environment: Readiness (Q6)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Production Status:</strong>
    <ul>
<li>Azure Cloud environment <strong>ready</strong></li>
<li>Deployment via <strong>TOS Patch</strong> after UAT sign-off</li>
</ul>
</li>
<li><strong>Deployment Process:</strong>
    <ul>
<li>Following successful UAT validation</li>
<li>With mutual sign-off on system stability</li>
</ul>
</li>
</ul>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-1-grad">
    <h1><span class="stage-number">12</span>Pilot Test in Production (1 Yard Block) - Approach (Q7)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Feasibility:</strong> <strong>Yes, single block pilot is recommended</strong> for controlled rollout</li>
<li><strong>Approach:</strong>
    <ul>
<li>AI activated for specific block via configuration</li>
<li>Other blocks use existing logic during pilot</li>
</ul>
</li>
<li><strong>Configuration:</strong>
    <ul>
<li>Isolate AI to pilot block</li>
<li>Ensure proper data flow</li>
<li>Fine-tune parameters for specific block</li>
</ul>
</li>
</ul>
<p style="margin-top:10px;"><em>Detailed plan to be developed after UAT</em></p>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-2-grad">
    <h1><span class="stage-number">13</span>TOS-Azure Link (Q8)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Connection Method:</strong>
    <ul>
<li>TOS Patch establishes automated connection</li>
<li>Between TOS Production DB/WAS and Azure services</li>
</ul>
</li>
<li><strong>Architecture:</strong>
    <ul>
<li>Follows confirmed architecture diagram</li>
<li>No changes foreseen from CLT's side</li>
</ul>
</li>
</ul>
  </div>
</section>

<section>
  <div class="slide-header-custom stage-3-grad">
    <h1><span class="stage-number">14</span>CLT On-site Visit: Enhanced Proposal (Q9)</h1>
  </div>
  <div class="slide-content-custom align-top">
    <ul>
<li><strong>Westports' Dates:</strong> June 16-20</li>
<li><strong>CLT's Proposal:</strong>
    <ul>
<li><strong>Extended Duration: June 9-20 (2 weeks)</strong></li>
<li><strong>Week 1:</strong> UAT support & issue resolution</li>
<li><strong>Week 2:</strong> PROD monitoring & stabilization</li>
</ul>
</li>
</ul>
<p style="margin-top:10px;"><em>Maximizing success and ensuring smooth transition</em></p>
  </div>
</section>

</div></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.6.1/reveal.min.js"></script>
<script>
Reveal.initialize({controls:true,progress:true,history:true,center:false,hash:true,
  slideNumber:"c/t",transition:"slide",backgroundTransition:"fade",width:"100%",
  height:"100%",margin:0.05});
</script>
</body>
</html>