Westports AI Pre-PoC 작업 목록

1. 클라우드 환경 설정  
   - 1.1. 클라우드 제공업체 선정  
     - 1.1.1. 클라우드 제공업체 평가  
     - 1.1.2. 클라우드 자원 할당 계획  
   - 1.2. 인프라 설정  
     - 1.2.1. 클라우드 환경 구성  
     - 1.2.2. 개발 및 테스트 환경 설정  
       - 1.2.2.1. Ubuntu 설치  
       - 1.2.2.2. AI 모듈 배포  
       - 1.2.2.3. Podman 설치  
       - 1.2.2.4. CDC (Debezium) 설정  
     - 1.2.3. 모니터링 및 로깅 시스템 구성  
       - 1.2.3.1. 모니터링 및 로깅 시스템 선정  
       - 1.2.3.2. 모니터링 및 로깅 시스템 구성  
     - 1.3. 보안 구성  
       - 1.3.1. VPN 연결 설정 완료  
       - 1.3.2. 방화벽 규칙 검토 및 업데이트  
       - 1.3.3. 접근 제어 구성

   

2. 기본 시스템 개발 및 데이터 통합  
   - 2.1. 야드 할당 로직 개선  
     - 2.1.1. 야드 할당 로직 설계  
     - 2.1.2. 야드 할당 로직 구현  
   - 2.2. IBS 모듈 구현  
     - 2.2.1. IBS 모듈 설계  
     - 2.2.2. IBS 모듈 구현  
     - 2.2.3. 이중화 서버 구성  
   - 2.3. 데이터 수집 및 통합 모듈 구현  
     - 2.3.1. 데이터 수집 모듈 설계  
       - *******. CDC Table 기반 데이터 수집 모듈 설계  
       - *******. TOS / Digiport 데이터 커넥터 구현  
     - 2.3.2. 데이터 변환 로직 설정  
     - 2.3.3. 데이터 수집 및 통합 모듈 구현  
     - 2.3.4. 기본 데이터 동기화 활성화  
   - 2.4. 서비스 운영 상태 대시보드 구현  
     - 2.4.1. 서비스 운영 상태 대시보드 설계  
     - 2.4.2. 서비스 운영 상태 대시보드 구현  
   - 2.5. TOS 인터페이스 업데이트  
     - 2.5.1. TOS 인터페이스 설계  
     - 2.5.2. TOS 인터페이스 구현  
   - 2.6. Dwell Time 예측 모형 개선 (Optional)  
   - 2.7. 할당 로직 검증 시뮬레이터 구현 (Optional)

3. CDC 구성  
   - 3.1. CDC 동작 수정 및 개선  
   - 3.2. CDC 동기화 응답 시간 단축  
   - 3.3. CDC 성능 개선

   

4. 요구사항 수집  
   - 4.1. 특정 POC 요구사항 및 범위 정의  
   - 4.2. POC 성공 기준 및 KPI 설정  
   - 4.3. 운영 제약 조건 및 제한 사항 식별

   

5. 초기 테스트  
   - 5.1. 시스템 연결 확인  
   - 5.2. 기본 데이터 흐름 테스트  
   - 5.3. 통합 검증
