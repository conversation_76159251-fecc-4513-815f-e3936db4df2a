**APPENDIX A**

(To be included and read as part of the contract)

Appendix A

**STATEMENT OF WORKS**

| Statement of Works for AI-Powered Yard Allocation System POC |
| :---: |

1. Project Proposal: Optimizing Terminal Operations with AI-Driven Yard Allocation at Westports

   1. Executive Summary

In today's rapidly evolving port logistics environment, embracing next-generation technology is critical to maintaining a competitive edge and maximizing operational efficiency. This proposal presents a Proof of Concept (POC) for CyberLogitec’s (CLT) AI-Powered Yard Allocation System at Westports. By leveraging advanced algorithms and predictive analytics, the system optimizes container placement, significantly reducing unproductive moves, maximizing yard space utilization, and ultimately driving increased productivity and cost savings at Westports.

1. Introduction: Addressing the Challenges of Modern Terminal Operations in Malaysia

   2. 

Modern port terminals, particularly within the dynamic Malaysian market, face increasingly complex operational challenges, including:

* **Surging Container Throughput:** The growth of global trade and Malaysia's strategic position in Southeast Asia place immense pressure on terminals to handle ever-increasing container volumes efficiently.  
* **Optimizing Yard Space Utilization:** Efficiently utilizing limited yard space is paramount to maximizing terminal productivity, particularly in land-scarce environments.  
* **Operating Cost Reduction:** In a highly competitive environment, minimizing operational costs is crucial for maintaining profitability and securing long-term sustainability.  
* **Meeting Stringent Regulatory Requirements:** Malaysian ports operate under strict regulations that demand high levels of efficiency and safety.

  2. Proposed Solution: CLT’s AI-Powered Yard Allocation System

| Module | Core Functions | Benefits |
| :---- | :---- | :---- |
| **Yard Allocation System** | **AI-Powered Intelligent Stacking Position Selection**:  Optimal container placement using AI and real-time data  **Inter Bay Shifting**:  Recommends target containers for IBS and their optimal positions based on vessel schedule and rehandling complexity.  **Prediction based Decision** : Forecasts positions to minimize reshuffling | **Reduced Unproductive Moves**: Minimizes unnecessary container shifts and streamlines turnaround times. **Improved Yard Efficiency**: Maximizes yard space utilization and accelerates container processing. **Enhanced Operational Visibility**: Real-time data visualization supports proactive decision-making. |

CLT proposes the POC implementation of its AI-Powered Yard Allocation System,   
a next-generation platform that transforms yard operations by determining optimal container placement in real time.

| Module | Core Functions | Benefits |
| :---- | :---- | :---- |
| Yard Allocation System | AI-Powered Intelligent Stacking Position Selection:  Optimal container placement using AI and real-time data Inter Bay Shifting:  Recommends target containers for IBS and their optimal positions based on vessel schedule and rehandling complexity. Prediction based Decision : Forecasts positions to minimize reshuffling | Reduced Unproductive Moves: Minimizes unnecessary container shifts and streamlines turnaround times. Improved Yard Efficiency: Maximizes yard space utilization and accelerates container processing. Enhanced Operational Visibility: Real-time data visualization supports proactive decision-making. |

20. Implementation Strategy: Phased Approach for Seamless Integration

A phased implementation approach ensures minimal disruption to ongoing operations while thoroughly validating system performance.

1. Proof of Concept (POC) Implementation Phase

| Feature | Description |
| :---- | :---- |
| **Objective** | To validate the effectiveness of the AI-Powered Yard Allocation System in a controlled environment within Westports, demonstrating its ability to reduce unproductive moves and improve yard efficiency. |
| **Duration** | 6 Months |
| **Target** | 15% reduction in unproductive moves within the designated POC area. |
| **Location** | 30 Yard Blocks at Westports. |
| **Methodology** | Data-driven analysis, continuous monitoring, and performance reporting using KPIs. |

Key Functions

| Function | Description | Benefits for Westports |
| :---- | :---- | :---- |
| **AI-Powered  Intelligent Stacking Position Selection** | Intelligently recommends stacking positions for each container within the designated yard blocks, minimizing travel distances and maximizing space utilization. | \- Optimized stacking for safety and efficiency \- Reduced reshuffling \- Improved retrieval times |
| **Inter Bay Shitfing** | Recommends target containers for IBS and their optimal positions based on vessel schedule and rehandling complexity. | \- Proactive reduction of complex rehandling operations \- Optimized pre-loading preparation  |
| **Prediction based Decision** | Makes data-driven operational decisions by analyzing historical patterns and predicting future schedules | \- Adaptive decision-making capability \- Consistent performance under varying conditions \- Resilient operations management |
| **Performance Measurement and Validation** | Continuous tracking and analysis of Key Performance Indicators (KPIs) such as unproductive moves, equipment utilization, and turnaround times. | \- Data-driven insights for optimization \- Demonstration of ROI \- Transparent reporting on project progress and success |

Key Functions

| Function | Description | Benefits for Westports |
| :---- | :---- | :---- |
| AI-Powered  Intelligent Stacking Position Selection | Intelligently recommends stacking positions for each container within the designated yard blocks, minimizing travel distances and maximizing space utilization. | \- Optimized stacking for safety and efficiency \- Reduced reshuffling \- Improved retrieval times |
| Inter Bay Shitfing | Recommends target containers for IBS and their optimal positions based on vessel schedule and rehandling complexity. | \- Proactive reduction of complex rehandling operations \- Optimized pre-loading preparation  |
| Prediction based Decision | Makes data-driven operational decisions by analyzing historical patterns and predicting future schedules | \- Adaptive decision-making capability \- Consistent performance under varying conditions \- Resilient operations management |
| Performance Measurement and Validation | Continuous tracking and analysis of Key Performance Indicators (KPIs) such as unproductive moves, equipment utilization, and turnaround times. | \- Data-driven insights for optimization \- Demonstration of ROI \- Transparent reporting on project progress and success |

Main Project Phase: Full-Scale Implementation

| Feature | Description |
| :---- | :---- |
| **Objective** | To extend the benefits of the AI-Powered Yard Allocation System across all yard zones at Westports, maximizing operational efficiency and achieving significant cost savings. |
| **Scope** | All yard zones at Westports. |
| **Target** | Minimum 20% reduction in unproductive moves for all yard areas |
| **Approach** | Phased rollout, informed by the results and learnings from the POC phase. |
| **Decision** | The full scope and timeline will be determined collaboratively with Westports after the successful completion and validation of the POC. |

2. Project Scope & Timeline

   1. Project Phases

      1. Pre-POC Phase (January 2025 – March 2025\)

| Task | Description | Timeline |
| :---- | :---- | :---- |
| **Cloud Environment Setup** | \- Infrastructure provisioning and configuration \- Security and access management \- Development & testing environments | January 2025 |
| **POC Preparation** | \- Data integration framework setup \- System interface configuration \- Testing environment validation | February – March 2025 |

      2. POC Phase (April 2025 – September 2025\)

| Task | Description | Timeline |
| :---- | :---- | :---- |
| **Yard Allocation System Implementation** | \- Coverage: 30 yard blocks  \- AI-powered container position optimization \- IBS functionality for target container selection and position recommendation \- Performance measurement and validation \- KPI monitoring and reporting | April – September 2025 |

      3. Main Project Phase (Post-POC)

      4. (Exact dates to be determined upon POC completion)

| Task | Description |
| :---- | :---- |
| **Extension of Yard Allocation System** | \- Full-scale deployment across **all yard zones** at Westports \- System performance optimization |
| **High Availability Setup** | \- Implementation of redundant server infrastructure \- Load balancer configuration \- Failover system implementation |

**Note**: The scope and timeline for the Main Project Phase will be finalized based on the POC results and a Go/No-Go decision at the end of September 2025\.

2. Project Timeline

Below is a *simplified* Gantt chart-style overview. “XXXX” denotes the duration of each task within that month.

| Task / Month | Jan 2025 | Feb 2025 | Mar 2025 | Apr 2025 | May 2025 | Jun 2025 | Jul 2025 | Aug 2025 | Sep2025 |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| **Pre-POC Phase** |  |  |  |  |  |  |  |  |  |
| Cloud Environment Setup | XXXX |  |  |  |  |  |  |  |  |
| POC Preparation | XXXX | XXXX | XXXX |  |  |  |  |  |  |
| **POC Phase** |  |  |  |  |  |  |  |  |  |
| Yard Allocation System Implementation |  |  |  | XXXX | XXXXXXX | XXXXXXX | XXXXXXX | XXXXXXX | XXXXXXX |
| **Testing & KPI Validation** |  |  |  | XXXXXXX | XXXXXXX | XXXXXXX | XXXXXXX | XXXXXXX | XXXXXXX |
| **Project Closure / Extension Decision** |  |  |  |  |  |  |  |  | **XXXX** |

**Pre-POC Phase:** January – March 2025  
**POC Phase:** April – September 2025  
**Main Project Phase:** Post-POC, subject to Go/No-Go decision in September 2025

3. Key Deliverables & Milestones

| Deliverable / Milestone | Target Date | Description |
| :---- | :---- | :---- |
| **Pre-POC Environment Setup** | January 2025 | Completion of cloud environment provisioning, security/access management, and development/test environments. |
| **POC Environment Validation** | Feb-Mar 2025 | Successful validation of data integration and system interfaces in a controlled POC environment. |
| **POC Project Initiation** | April 2025 | Formal launch of the POC phase and commencement of Yard Allocation System testing in the designated yard blocks. |
| **System Implementation & Integration** | Throughout POC (Apr–Sep 2025\) | Deployment of the AI-powered Yard Allocation System within the specified POC blocks, including: \- AI-powered container position optimization \- IBS functionality with target container selection and position recommendation  \- Real-time data integration and system interfaces |
| **Testing & KPI Validation** | Throughout POC (Apr–Sep 2025\) | Continuous monitoring and measurement of unproductive moves, equipment utilization, and overall yard efficiency. |
| **Project Closure / Extension Decision** | September 2025 | Final review of the POC’s outcomes (KPI improvements, cost savings, operational enhancements) to determine next steps. |
| **Main Project Preparation** | October 2025 | Contract negotiation and finalization, system expansion planning for all yard blocks, and detailed implementation timeline and resource allocation. |
| **Main Project Implementation** | October 2025 – September 2026 | Full-scale deployment of Yard Allocation AI Service across all yard zones at Westports. |

3. Project Organization

   1. Project Team Structure

The project will be executed by a team of experts in various fields, including project management, AI development, system engineering, and on-site operations support, ensuring its successful completion.

![A computer screen shot of a diagramAI-generated content may be incorrect.][image1]

| Role | Name (CLT) | Years of Experience | Expertise | Responsibilities |
| :---- | :---- | :---- | :---- | :---- |
| Project Director | Jun-Hee Cho [<EMAIL>](mailto:<EMAIL>)  | \[20+\] years in port automation and AI solutions | Strategic planning, stakeholder management, risk management, project delivery | Overall project oversight, strategic direction, stakeholder communication, risk management, issue resolution, final decision-making authority.  |
| Project Manager / Technical Lead | Jeong-Min Kim [<EMAIL>](mailto:<EMAIL>)  | \[12+\] years in AI/ML development | AI/ML, software architecture, cloud technologies, system design | Technical architecture and design, AI model development and optimization, system integration oversight, performance monitoring, and optimization. Backup contact for Westports. |
| Integration Lead | Jae-Ho Jung [<EMAIL>](mailto:<EMAIL>) | \[12+\] years in system integration | System integration, API design, data pipelines, TOS integration | TOS integration planning and execution, data flow management, interface design and implementation, system testing coordination. |
| AI Engineers | Tae-Kwang Kim [<EMAIL>](mailto:<EMAIL>) | \[10+\] years in AI/ML development | Machine learning model development, algorithm optimization, performance tuning, technical documentation | Development, testing, and optimization of AI/ML models. |
| System Engineers | Sung-Gook Wi [<EMAIL>](mailto:<EMAIL>) | \[20+\] years in system Engineering and infra | Cloud infrastructure, system monitoring, security implementation, performance optimization | Cloud infrastructure management, system monitoring, security implementation, performance tuning. |
| Integration Engineers | Hi-Su Kim [<EMAIL>](mailto:<EMAIL>) | \[20+\] years in system integration | API development, data pipeline implementation, interface testing, integration documentation | Development and implementation of APIs, data pipelines, and system interfaces. |
| OPUS Terminal(TOS) Developers | Dong-Gyun Lee [<EMAIL>](mailto:<EMAIL>) | \[10+\] years in OPUS Terminal development | OPUS Terminal functionalities, APIs, data structures, customization	 | Provide technical expertise on OPUS Terminal, develop necessary customizations or APIs for integration, support the Integration Lead and Customer Comms & TOS BA in resolving TOS-specific issues, participate in testing and validation related to TOS integration. |
| Customer Comms & TOS BA | Ganesh Kumar [<EMAIL>](mailto:<EMAIL>) | \[10+\] years in port operations/BA | Port operations, TOS functionalities, requirements gathering, stakeholder communication, process analysis | Manage customer communications, gather and document requirements related to TOS integration, act as a liaison between Westports and the development team, facilitate UAT, and support the Project Director in managing stakeholder expectations, especially those related to the TOS. Primary contact for Westports. |

2.      Single Point of Contact

   3. 

* Primary Contact  
* Role: Customer Comms & TOS BA  
* Email: [<EMAIL>](mailto:<EMAIL>)  
* Available: 24/7 for critical issues

* Backup Contact  
* Role: Project Manager / Technical Lead  
* Email: [<EMAIL>](mailto:<EMAIL>)  
* Available: During standard business hours

  4. RACI Matrix

| Phase | Activity | RACI |  |
| :---- | :---- | :---: | :---: |
|  |  | **CLT** | **Westports** |
| **Pre-POC** | **Cloud Provider Selection** • Evaluate cloud providers (Azure, AWS, GCP) • Plan cloud resource allocation | R/A | C |
|  | **Infrastructure Setup** • Configure cloud environment • Setup development and test environments • Configure monitoring and logging systems | R/A | I |
|  | **Security Config** • Set up VPN and network security • Configure access controls | R/A/C | C |
|  | **Basic System Development** • Implement core system components • Develop essential API endpoints • Set up initial data processing pipeline | R/A/C | C |
|  | **TOS & External System Basic Integration** • Implement TOS / Digiport data connectors • Set up data transformation logic • Enable basic data synchronization | R/A/C | C |
|  | **Requirements Gathering** • Define specific POC requirements and scope • Establish POC success criteria and KPIs • Identify operational constraints and limitations | R/A/C | C |
|  | **Initial Testing** • Verify system connectivity • Test basic data flow • Validate integration points | R/A/C | C |
| **POC** | **UAT Testing for Field Implementation** • Set up virtual test environment • Simulate real operation scenarios • Validate system behavior | R/C/I | A |
|  | **AI Model Development & Improvement** • Develop yard allocation algorithms • Develop IBS candidate generation algorithms • Train models with historical data • Implement optimization logic • Fine-tune model parameters | R/A/C | I |
| **POC** | **Advanced Feature Development** • Enhance UI components • Implement advanced business logic • Develop reporting functions | R/A/C | I |
|  | **Integration Enhancement** • Expand TOS & Digiport data integration • Optimize data synchronization | R/A/C | C |
|  | **Performance Validation** • Measure system performance • Validate optimization results • Compare with current operations • Document efficiency gains | R/A/C | C |
|  | **Project Closure** • Review user feedback and satisfaction • Make go/no-go decision for main project | R/C | A |
| **Main Project** | **Expansion Planning** • Plan full-scale deployment • Define complete resource requirements • Create detailed implementation timeline • Identify scaling considerations | R/C | A |
|  | **Full Implementation** • Deploy to production environment • Implement all planned features • Set up comprehensive monitoring • Enable advanced functionalities | R/A/C | C |
|  | **Complete Integration** • Implement full TOS / Digiport integration • Set up failover mechanisms | R/A/C | C |
|  | **Product Deployment** • Conduct full system testing • Perform load testing • Complete user training | R/A/C | C |

Legend:

* R (Responsible):  Does the work.  
* A (Accountable): Ensures completion and has final approval.  
* C (Consulted): Provides input and expertise.  
* I (Informed): Kept updated on progress.

  5. Communication Plan

     1. Key Stakeholders

* Westports Team:  
* Proejct Management:   
  * Tan Wei Chun (IT GM)  
    *  Visahan (IT HOS Ops and Support)  
* Technical Team:   
  * Nadarajan (Terminal Planning HOD)  
    * Vijayavarma (Yard Planning HOS)  
    * Ikhsan Sazali (Yard Planning Manager)  
    *  Mohd Haikal (Yard Planning Manager)  
    *  Hazrul Amin (IT HOS Development) 

* CyberLogitec Team  
* Project Management:   
  * Jun-Hee Cho (Project Director)  
    *  Jeong-Min Kim (Project Manager / Technical Lead)  
    * Ganesh Kumar (PMO)  
* Technical Team:   
  * Jae-Ho Jung (Integration Lead)  
    * Tae-Kwang Kim (AI Engineer)  
    * Sung-Gook Wi (System Engineer)  
    * Hi-Su Kim (Integration Engineer)  
    *  Dong-Gyun Lee (OPUS Terminal Developer)

    2. 

    3. 

    4. 

    5. 

    6. 

    7. 

    8. 

    9. 

    10. 

    11. Communication Activities

    12. 

| Communication Activity | Frequency | Participants | Objectives | Communication Method |
| ----- | ----- | ----- | ----- | ----- |
| Weekly Status Meetings | Weekly | At least one management representative and one technical staff from each side | • Review project progress against planned activities. • Discuss and address any roadblocks or issues. • Coordinate tasks and dependencies. • Document decisions and action items. | Face-to-face meeting or video conference |
| Bi-weekly Progress Reports | Bi-weekly (every two weeks) | Both management representatives and at least one technical staff from each side | • Provide a comprehensive overview of project status, including progress, risks, and issues. • Highlight key achievements and milestones. • Report on budget and resource utilization. • Communicate any changes to scope, timeline, or budget. | Formal written report (e.g., PDF document) distributed via email |
| Ad-hoc Technical Discussions | As needed | At least one management representative and one technical staff with relevant expertise from each side | • Address specific technical challenges or integration issues. • Collaborate on solutions and ensure technical alignment. • Document decisions and action items. | Face-to-face meeting, video conference, or phone call |
| Formal Phase Gate Reviews | At the end of each phase (Pre-POC, POC) | Westports Project Sponsor, Steering Committee (if applicable), All management representatives and key technical staff from each side | • Review the deliverables and outcomes of the completed phase. • Assess project performance against defined objectives and KPIs. • Make a go/no-go decision for the next phase. • Approve any necessary changes to scope, timeline, or budget. | Formal meeting with a presentation and supporting documentation |

4. 

5.   
6.   
7.   
8. System Architecture and Technical Requirements

   1. System Architecture

The AI-Powered Yard Allocation System consists of four main components that work together to deliver intelligent yard operations. For this PoC phase, we will focus on implementing the core Yard Allocation functionality.  
![A diagram of a diagramAI-generated content may be incorrect.][image2]

1. AI Service Platform (PoC Coverage)

* Dashboard App (PoC Coverage):  
* Provides real-time visualization and monitoring of yard operations, container placements, and system performance metrics.  
* Yard Allocation Service (PoC Coverage, Primary Focus)  
* AI-driven determination of optimal yard stacking locations.  
* Container dwell time prediction to minimize congestion and optimize yard usage.  
* Rehandling estimation and reduction strategies for improved operational efficiency.  
* IBS Recommendation Service (PoC Coverage, Primary Focus)  
* Recommends target containers for IBS and their optimal positions based on vessel schedule and rehandling complexity.  
* Integration interfaces (CDC, TOS, Digiport)  
* Seamless data exchange and interoperability with existing terminal operating systems.  
* AI Data Manager for data processing  
* Data ingestion, preprocessing, and management for AI/ML modules.  
    
  2. Digiport IoT Platform (Optionally implemented for POC Phase)

DigiPort can be implemented during the POC phase to enhance AI model performance. The system is architected with modularity in mind, allowing for flexible post-POC decisions:

* AI Interface (PoC Coverage)  
* Facilitates secure and reliable integration with the AI Service Platform.  
* Virtual Terminal Interface (Future/Partial Coverage)  
* Enables real-time visibility into yard activities.  
* TOS Interface (Future/Partial Coverage)  
* Integrates with the Terminal Operating System (TOS) to exchange job orders, container inventory, and scheduling data.  
* Crane, VMT for TT, and Haulier Interfaces (Future/Partial Coverage)  
* Provides an interface to link with existing WestPorts systems to monitor the status and location of cranes terminal trucks and external trucks.

  3. Machine Learning Platform (Future Phase)

* Operational Policy Manager:  
* Enforces rule-based decision-making logic within the AI ecosystem.  
* Prediction Model Manager:  
* Provides forecasting and optimization models for terminal operations.  
* Interfaces with AI Data Manager:  
* Continuous learning and model updates based on real-time and historical data

**Notes**

* DigiPort components will be implemented during POC to validate enhancement potential  
* Core AI functionality remains intact regardless of post-POC DigiPort decisions.  
  2. Network Infrastructure

     1. Overview

![A screenshot of a computerAI-generated content may be incorrect.][image3]  
The diagram above illustrates the Yard Allocation PoC network infrastructure, highlighting how various on-premise systems securely connect to a centralized PoC Server hosted on Microsoft Azure (SouthEast Asia Region). The PoC Server runs containerized logical modules under Podman—such as the Yard Allocation Module, IBS Module, Monitoring Dashboard, Kafka Client, and Debezium (CDC)—to enable real-time data processing and yard optimization.

* Azure Cloud (Yard Allocation Server)  
* Runs the Yard Allocation Module (Port 18000, REST API), IBS Module (Port 18001, REST API), Monitoring Dashboard (Port 18002), and Kafka Client.  
* Utilizes Debezium 3.x for CDC (Change Data Capture) to track and process TOS database updates in real time.  
* All communication is routed through secure VPN tunnels to ensure data integrity and confidentiality.  
    
*   
* On-Premise Systems  
* **PNO-WAS**: Primary and backup servers (e.g., *********, *********9) send container movement and operational planning data to the Yard Allocation Module via a REST API.  
* **TOS Database**: Connected through Debezium, which captures every data change (INSERT, UPDATE, DELETE) and forwards it to the Yard Allocation Module, enabling real-time synchronization.  
* **Digiport Server (IoT Platform)**: A minimal version of the IoT platform is deployed on-premise. It provides sensor data (e.g., equipment status) to the PoC Server through a secure VPN connection.  
* **Haulier Booking System**: An external system that exchanges scheduling and booking information via REST API calls.

Communication Protocols & Security

* **REST API (JSON)**: Standard format for exchanging container movement data, yard allocation results, and monitoring metrics.  
* **Kafka**: Used for message queuing and real-time data streaming between the Yard Allocation Module and other services.  
* **Debezium CDC**: Monitors database transactions in the TOS environment and pushes changes to the PoC Server.  
* **VPN Tunnels (IPSec, AES-256 Encryption)**: Ensures that all traffic between the cloud-based PoC Server and on-premise systems remains encrypted and secure.

High Availability Configuration

* Azure Cloud Environment  
* Multiple Kubernetes pods for redundancy  
  * 1 For Pre-PoC and UAT testing (Internal IP: ********, External IP: ***********),   
    * 2 For Production with Active/Standby configuration   
  * Automatic failover between production pods ensures continuous service.  
* VPN Connectivity  
* **Redundant VPN gateways**: Primary and backup gateways (************, ************)  
* Three separate VPN connections for each instance  
* Automatic failover to backup gateway in case of primary gateway failure  
* Data Synchronization  
* Debezium 3.x ensures reliable CDC (Change Data Capture)  
* Kafka-based message queuing for resilient data transfer  
* Automatic recovery and resynchronization after connection restoration

Network Requirements

* Connectivity Requirements  
* **Minimum Bandwidth**: At least **100 Mbps** network connection between on-premise systems and the PoC server.  
* **Latency**: Round-trip latency must not exceed **50ms** between the primary components (e.g., Yard Allocation Module, TOS DB, PNO-WAS).  
* **Monitoring & Alerts**: A basic network monitoring and alert system should be in place to detect and address latency spikes, bandwidth issues, or connection failures in a timely manner.  
* VPN Configuration  
* **Site-to-Site VPN**: A dedicated, site-to-site IPSec VPN tunnel must be established between the on-premise network and the Azure PoC environment.  
* **Encryption**: **AES-256** encryption is required to ensure secure data transmission.  
* **VPN Monitoring**: Basic VPN health checks, such as uptime monitoring and tunnel status alerts, should be enabled to promptly identify any disruptions or security anomalies.  
* Monthly Traffic Estimation  
* Approx. 700GB of total data transfer over the course of the PoC.  
* Data Flows and Bandwidth Usage  
  * **Real-Time Data** (approx. **5 Mbps** per stream):  
    - Yard inventory status  
    - Container properties  
    - Processing history  
    * Periodic Updates (approx. 0.5–1 Mbps per update):  
    - Vessel schedules  
    - QC (Quay Crane) schedules  
    - Equipment status  
    - Operation records  
    * Dashboard Access  
    - Estimated **1 Mbps** bandwidth requirement per user

  3. Server Requirements for PoC

     1. Cloud-based AI Service (Azure)

Compute Resources

* Virtual Machine: NC16as T4 v3 (or higher)  
* vCPUs: 16 cores (up to 32\)  
* RAM: 64GB (up to 96GB)  
* OS: Ubuntu 24.04  
* Storage: Premium SSD 1TB  
  * IOPS: 5,000  
    * Throughput: 200 MB/sec

Required Applications

* Podman 5.3 or higher  
* Postgres 17.2 or higher  
* Python 3.12 or higher  
* Debezium 3.0 or higher

Network

* Location: Southeast Asia (Azure region)  
* Bandwidth: Up to 100 Mbps

  2. On-premise Digiport Server

Hardware Specifications (For PoC Period Only)

* CPU: 8 Core  
* RAM: 64GB  
* Storage: 500GB  
* Network: Up to 1000 Mbps

Software Configuration (Temporary Setup)

* OS: Windows Server 2019

Network Setup

* ********** (to be assigned by Westports)  
* Required ports: 18000, 18001 (configurable)

  4. Integration Requirements

     1. TOS Database Integration

* Yard Inventory Status  
* Real-time updates of container positions and yard occupancy.  
* Equipment Status Monitoring  
* Live tracking of Quay crane, RTG, and TT operations for immediate situational awareness.  
* Operation Records Synchronization  
* Automatic syncing of operational logs (e.g., move events, job orders) to maintain consistent data across systems.

  2. Haulier Booking System Integration

* Truck Arrival Information  
* Real-time updates of external truck status and location.  
* Booking Schedule Management  
* Synchronization of container pickup and delivery appointments .  
  (including target container information).

  5. Security Requirements

     1. Encryption

* **SSL/TLS** for all data transfers to safeguard information integrity and confidentiality.	

  2. Network Security

* **Secure VPN Connection** between the cloud environment and on-premise systems (IPSec with AES-256 encryption).

  3. System Updates

* **Regular Security Patches and Updates** applied to both operating systems and application software.

  4. Access Control

* **Role-Based Access Control (RBAC)** implemented to ensure appropriate user privileges and restricted system access.  
9. Support & Maintenance

   1. Service Level Agreement (SLA)

      1. Incident Response Time

| Severity | Description | Response InTime | Work-around / Resolution Time |
| :---- | :---- | :---- | :---- |
| **Tier 1** | System down or major function unavailable | 2 hours | Temporary Work-around: 4 hours Target Resolution: 10 working days |
| **Tier 2** | Major function impaired | 24 hours | Temporary Work-around: 5 working days Target Resolution: 20 working days |
| **Low** | Non-critical issue | 5 working days | Target Resolution: in next release |

      2. Service Recovery Process

1\. Provide service status Monitoring

*             \- Web-based status monitoring page for real-time service status display  
*             \- Continuous service status logs for tracking and analysis

        2\. Automated Fallback Process

*             \- Automatic transition to TOS yard allocation system when AI system is compromised  
*             \- Immediate email notifications to system administrators for prompt action

        3\. Service Recovery and AI System Restoration

*             \- Systematic process for service recovery and system normalization  
*             \- Automated return to AI system operations once stability is confirmed

  2. **Support Services on Cloud Environment**  
* **Included Services**  
* 24/7 System Monitoring: Proactive alerts and incident response for cloud-based systems.  
* Performance Optimization: Ongoing tuning of system resources and configurations.  
* Security Patches and Updates: Regular application of OS, platform, and security updates.  
* Backup Management: Daily or weekly backups with defined retention policies.  
* **Exclusions**  
* Hardware Maintenance: Any replacement or repair of underlying physical infrastructure.  
* Network Infrastructure: WAN/LAN and internet backbone are managed by the provider.  
*   
* End-User Devices: Setup, troubleshooting, or maintenance of user workstations etc.  
* 

  3. **Maintenance Windows**  
     1. Scheduled Maintenance

* **Frequency**: Monthly  
* **Duration**: Maximum of 4 hours (System downtime is less than 1 hour)  
* **Advance** **Notice**: 5 business days prior to scheduled downtime  
  2. Emergency Maintenance

* **Purpose:** For critical security patches or urgent fixes that cannot be deferred  
* **Minimum Notice:** 4 hours before initiating emergency work  
* **Approval**: Must be approved by Westports before proceeding  
* 

Support Services on Cloud Environment  
Included Services  
24/7 System Monitoring: Proactive alerts and incident response for cloud-based systems.  
Performance Optimization: Ongoing tuning of system resources and configurations.  
Security Patches and Updates: Regular application of OS, platform, and security updates.  
Backup Management: Daily or weekly backups with defined retention policies.  
Exclusions  
Hardware Maintenance: Any replacement or repair of underlying physical infrastructure.  
Network Infrastructure: WAN/LAN and internet backbone are managed by the provider.  
End-User Devices: Setup, troubleshooting, or maintenance of user workstations etc.  
Maintenance Windows  
Scheduled Maintenance  
Frequency: Monthly  
Duration: Maximum of 4 hours (System downtime is less than 1 hour)  
Advance Notice: 5 business days prior to scheduled downtime  
Emergency Maintenance  
Purpose: For critical security patches or urgent fixes that cannot be deferred  
Minimum Notice: 4 hours before initiating emergency work  
Approval: Must be approved by Westports before proceeding

10. Terms and Conditions

    1. Project Duration and Phases

       1. Pre-POC Phase (3 months)

* **Period**: January 2025 \- March 2025  
* Performs cloud environment setup, system integration and data preparation.

  2. POC Phase (6 months)

* **Initial period**: April 2025 \- September 2025  
* Maximum extension: Additional 6 months with Westports approval  
* Justification for the extension must be provided before proceeding

  3. Main Project Implementation Phase (12 months)

* **Initial period**: October 2025 \- September 2026  
* Full implementation of AI Yard Allocation system  
* Monthly KPI monitoring and achievement-based subscription payments  
* Regular system updates and improvements

  2. Cost Structure and Payment

     1. Budget Details for POC period

* The total cost for the POC will be RM286,470  
* Cloud hosting fees shall be covered by Westports during the POC  
* Travel and living expenses shall be covered by Westports during the POC  
* Costs exceeding the project budget shall be borne by CLT

| POC Cost (Yard Allocation AI Service) for 9 months |  |  |  |
| :---- | :---- | :---- | :---- |
| **Category** | **Vendor** | **Cost (USD)** | **Cost (MYR)** |
| Cloud Server (9 months: Jan 2025 – Sep 2025\)  | CLT | 31,500 (3,500 monthly) | 141,750 (15,750 monthly) |
| 3rd party miscellaneous cost  (Travel, Lodging, Project Consulting Cost) | CLT | 32,160 | 144,720 (3 onsite visits) |
| Overall Total  (1USD – MYR4.5) | CLT | 63,660 | 286,470 |

\* All fees are expressed net of any taxes or duties (such as sales tax or value added tax).

2. Extension Period Costs

* Extension period funding requires reasonable justification from CLT  
* Upon approval:  
* Monthly costs shall be maintained at the initial POC rate  
* Cloud hosting and operational expenses coverage shall continue  
* 3rd party charges (Travel, Lodging, Project Consulting Cost) will be waived during the extension period

  3. Budget Details for Main Project

* The total cost for the Main Project will be RM495,720 \+ RM37,500 (monthly) Opex cost.  
* Cloud hosting fees shall be covered by Westports during the POC  
* Travel and living expenses shall be covered by Westports during the POC  
* Costs exceeding the project budget shall be borne by CLT  
* A separate LOA will be awarded later to our outsourcing partner for On-Premise Server Purchase and Support for the Main Project

| Main Project Cost – (Yard Allocation AI Service) for Capex |  |  |  |
| :---- | :---- | :---- | :---- |
| **Category** | **Vendor** | **Cost (USD)** | **Cost (MYR)** |
| Cloud Server (12 months: Oct 2025 – Sep 2026\)  | CLT | 42,000 (3,500 monthly) | 189,000 (15,750 monthly) |
| Cloud Maintenance (12 months: Oct 2025 – Sep 2026\) | CLT | 36,000 (3,000 monthly) | 162,000 (13,500 monthly) |
| 3rd party miscellaneous cost  (Travel, Lodging, Project Consulting Cost) | CLT | 32,160 | 144,720 (3 onsite visits) |
| Overall Total  (1USD – MYR4.5) | CLT | 110,160 | 495,720 |
| **Main Project Cost – (Yard Allocation AI Service) for Opex** |  |  |  |
| **Category** | **Vendor** | **Cost (USD)** | **Cost (MYR)** |
| Software Subscription (USD8,333 monthly from Oct 2026\) | CLT | 100,000 (8,333 monthly) | 450,000 (37,500 monthly) |

| Main Project Cost – (Yard Allocation AI Service) for Opex |  |  |  |
| :---- | :---- | :---- | :---- |
| **Category** | **Vendor** | **Cost (USD)** | **Cost (MYR)** |
| Software Subscription (USD8,333 monthly from Oct 2026\) | CLT | 100,000 (8,333 monthly) | 450,000 (37,500 monthly) |

* Monthly payment of USD8,333 is only applicable if CLT meets minimum 20% KPI of unproductive moves reduction which is to be approved by Westports.

  4. Payment Schedule and Terms

     5. 

* Invoice Currency and Payment Terms:  
* All invoices shall be based on USD amounts and issued in USD  
* Payment terms: Net 60 days from invoice date  
* Monthly invoices will be issued at the end of each month  
* Cloud hosting fees:   
* Charged based on actual usage, up to maximum USD 3,500 per month  
* Monthly invoices for monthly settlement  
* Travel & Living Expenses  
* Three planned trips over six months PoC project  
* Three team members per trip, approximately two weeks each  
* Covers:  
1) Air travel \- Economy class, MH and KAL preferred airlines  
2) Accommodation: hotel accommodation inclusive of breakfast  
3) Transportation: hotel to westports and vice versa  
* Retention Fee:  
* 15% will be withheld from each invoice  
* Retained amount to be paid in August 2026 (one year after project completion)

| Account Details |  |
| :---- | :---- |
| Bank Name:  | KEB HANA BANK  |
| Bank Address:  | YEOUIDO BRANCH, SEOUL, KOREA |
| Account Number: | 149-890070-64638 |
| SWIFT BIC: | KOEXKRSE |
| Account Holder:  | CYBERLOGITEC.CO.LTD |

3. 

   4. Project Success Criteria

All KPI measurements and targets specified in this section are based on monthly averages, providing a consistent and reliable measure of performance improvement.

1. KPI Achievement

* Measurement shall be based on:  
* Yard Density  
* Vessel Volume  
* Unproductive Moves   
* Target Blocks:  
* POC Phase: 30 yard blocks  
* Main Proejct: All yard blocks   
* Specific KPI targets shall be defined in separate KPI Benchmarking tables

  2. Contract Progression

* Initial 6-month PoC  
* First 3 months: Performance improvement phase with justification process  
* Last 3 months: Mandatory achievement phase  
* Upon achieving the KPIs: Proceed to the Main Contract  
* The KPI evaluation period shall be 1 year from implementation  
* Extension Period (if required)  
* Maximum 6 months duration  
* Success criteria: 15% reduction for 3 consecutive months  
* 1 Year Main Project Implementation  
* Target performance: Minimum 20% rehandling reduction  
* Measurement Methodology: Identical to PoC phase evaluation methods  
* Performance evaluation will follow the same statistical analysis and reporting procedures used during PoC  
  3. Service Launch Requirements and Penalties

* AI Service Production Launch Timeline  
* Preparation and Testing Period: January 1 to March 31, 2025  
* Required Production Launch Date: April 1, 2025  
* AI Service Production Launch Requirements  
* AI system must be fully integrated and operational with the production TOS server  
* Integration must be verified in the production environment  
* CLT must formally notify Westports when the system is ready for production  
* Launch is officially recognized only after Westports' formal approval  
* Penalty for Delayed Launch   
* Applicable from April 1, 2025  
* Daily penalty rate: MYR 500 per day of delay  
* Maximum penalty cap: MYR 28,647 (represents 10% of total contract amount, equivalent to 57 days of delay)

  4. Target Timeline

The success of this POC will be evaluated based on achieving progressive reduction in unproductive moves over the 6-month period:

| Month | 1st month | 2nd month | 3rd month | 4th month | 5th month | 6th month |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| **Required KPI (Per each month)** | 5% | 5% | 7.5% | 10% | 12.5% | 15% |

* First Phase (Month 1-3)  
* If targets are not met, CLT shall provide root cause analysis, system improvement plans and implementation timeline  
* Focus on system optimization and stabilization  
* Second Phase (Month 4-6)  
* Mandatory achievement of specified KPI targets  
* Performance review by Westports for next steps  
* Extension Phase (if required)  
* Duration: Up to 6 months  
* Target: Consistent 15% reduction or higher  
* Success: Achievement for 3 consecutive months  
    
  5. Measurement Method

**Scope and Monitoring**

* Focused 30 active blocks (K/L/M)  
* Dedicated task force team will monitor these blocks throughout the POC period  
* Performance data will be collected and analyzed daily

**Density-Based Calculation** The following table, derived from target blocks, shows the relationship between yard density and unproductive moves:

| Density Range | Frequency by Block  | Base Unproductive % |
| :---- | :---- | :---- |
| \>100% | 13 | 102% |
| 95-99% | 75 | 135% |
| 90-94% | 48 | 110% |
| 85-89% | 48 | 92% |
| 81-84% | 35 | 52% |
| 75-80% | 75 | 46% |
| Below 75% | 66 | 48% |

**Calculation Example** Example demonstrating how 5% reduction achievement is calculated based on yard density:

| Month | Yard Density | Vessel Volume form 29 blocks | Base Unprod % | Base Unprod Vol | Target Reduction | Required Vol Reduction | Actual Unprod Vol | Achievement Status |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| April 25 | 101% | 60,000 | 102% | 61,200 | 5% | 3,060 | 55,000 | Achieved (10.1%) |
| May 25 | 85% | 50,000 | 92% | 46,000 | 5% | 2,300 | 42,000 | Achieved (8.6%) |

**Calculation Steps**

1. Determine current yard density and corresponding base unproductive %  
   - Example: At 101% density, base unproductive moves is 102%

2. Calculate base unproductive volume  
   - Base Unproductive Volume \= Vessel Volume × Base Unproductive %  
   - Example: 60,000 × 102% \= 61,200 moves

3. Calculate required volume reduction for 5% target  
   - Required Reduction \= Base Unproductive Volume × 5%  
   - Example: 61,200 × 5% \= 3,060 moves

4. Compare with actual unproductive volume  
   - Achievement % \= (Base Unprod Vol \- Actual Unprod Vol) ÷ Base Unprod Vol × 100  
   - Example: (61,200 \- 55,000) ÷ 61,200 × 100 \= 10.1%

5. Determine achievement status  
   - If actual reduction ≥ 5%, target is achieved  
   - Example: 10.1% \> 5%, therefore target is achieved

   5. Termination Conditions

      1. KPI Failure

* For initial 6-month POC:  
* If mandatory KPI targets in Second Phase are not met, Westports will review the POC performance  
* Based on the review, Westports may approve extension period for further improvements, adjust targets or requirements or determine project continuation  
* For extension period:  
* Failure to achieve 15% target for consecutive 3 months within the extension period  
* The total extension period shall not exceed 6 months  
* Project termination shall be based on Westports' final assessment and decision

  2. Transition Support

* Upon project termination, CLT shall assist in developing the Interface method for integration with other Digital Twin providers  
* Integration support shall be provided via Direct DB \- Oracle GoldenGate or Redhat Debezium

  6. Project Implementation

     1. Infrastructure

* The cloud provider shall be selected by CLT  
* Infrastructure must meet the following requirements:  
* Project budget compliance  
* IT security standards  
* Westports operational requirements  
* Flexibility in resource allocation  
* Clear termination terms  
* The same infrastructure shall be maintained from POC through first year of implementation

  2. Support and Maintenance

* CLT shall provide technical support during POC period  
* System monitoring and maintenance shall be performed regularly  
* Performance optimization and updates shall be implemented as needed

7. Intellectual Property Rights

   1. Ownership

* AI model and core technology: CLT  
* Client data and business rules: Westports

  2. Usage Rights

* CLT maintains rights to core technology  
* Westports is granted a license for customized solutions during the POC Phase and the Extension Period if applicable. The license during the Main Project Phase will be determined on the Main Contract.

  8. Legal Compliance

     1. Confidentiality

* Both parties shall maintain strict confidentiality

  2. Liability

* CLT shall be responsible for system performance  
* Force majeure conditions shall apply  