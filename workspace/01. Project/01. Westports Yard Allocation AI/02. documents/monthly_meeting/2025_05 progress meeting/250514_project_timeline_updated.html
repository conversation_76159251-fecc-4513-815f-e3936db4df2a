<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Westports Yard Allocation AI Project - Timeline Visualization</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        /* Basic styling for the page */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .page-container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .timeline-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow-x: auto; /* Enable horizontal scrolling for timeline */
            min-height: 600px; /* Ensure enough vertical space */
        }
        /* Ensure the Mermaid chart has enough space */
        .mermaid {
            min-width: 1400px;
            margin: 0 auto;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #3498db;
            margin-top: 25px;
        }
        /* Table styling */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        /* Status styling */
        .done {
            color: #27ae60; /* Green */
            font-weight: bold;
        }
        .active {
            color: #f39c12; /* Orange */
            font-weight: bold;
        }
        .almost-done {
            color: #16a085; /* Teal */
            font-weight: bold;
        }
        .delayed {
            color: #e74c3c; /* Red */
            font-weight: bold;
        }
        .critical {
            color: #c0392b; /* Dark Red */
            font-weight: bold;
        }
        /* Progress bar styling */
        .progress-container {
            width: 100%;
            background-color: #e0e0e0;
            border-radius: 4px;
            margin: 5px 0;
            overflow: hidden; /* Ensure inner bar respects border radius */
        }
        .progress-bar {
            height: 20px;
            border-radius: 4px;
            background-color: #3498db; /* Default blue */
            text-align: center;
            color: white;
            font-weight: bold;
            line-height: 20px; /* Vertically center text */
            transition: width 0.5s ease-in-out; /* Smooth transition for width changes */
        }
        .progress-bar.delayed {
            background-color: #e74c3c; /* Red for delayed progress bar */
        }
        .progress-bar.almost-done {
            background-color: #1abc9c; /* Teal for nearing completion bar */
        }
        .progress-bar.done { /* Added style for completed items in table */
            background-color: #27ae60; /* Green */
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                gantt: {
                    titleTopMargin: 25,
                    barHeight: 25,
                    barGap: 6,
                    topPadding: 75,
                    bottomPadding: 50,
                    fontFamily: 'Arial',
                    fontSize: 14,
                    numberSectionStyles: 4,
                    axisFormat: '%m/%d',
                    tickInterval: '1week'
                }
            });
        });
    </script>
</head>
<body>
    <div class="page-container">
        <div class="container">
            <h1>Westports Yard Allocation AI Project Timeline</h1>
            <p>Updated visualization of project progress and upcoming milestones as of May 14, 2025.</p>
        </div>

        <div class="timeline-container">
            <div class="mermaid">
gantt
    title Westports Yard Allocation AI Project Timeline (Mid-April to July)
    dateFormat YYYY-MM-DD
    axisFormat %m/%d
    todayMarker stroke-width:3px,stroke:#FF0000,opacity:0.7
    
    %% Project Milestones
    section Milestones
    Current Date                :milestone, ms1, 2025-05-14, 0d
    YA AI & IBS UAT Deployment  :milestone, ms2, 2025-05-21, 0d
    Dashboard UAT Deployment    :milestone, ms3, 2025-05-26, 0d
    Production Go-Live          :milestone, ms4, 2025-06-06, 0d
    On-site Support (Week 1)    :milestone, ms5, 2025-06-09, 0d
    On-site Support (Week 2)    :milestone, ms6, 2025-06-16, 0d
    Project Completion          :milestone, ms7, 2025-06-30, 0d
    
    %% Infrastructure
    section Infrastructure
    Production Environment Setup :active, infra1, 2025-05-15, 2025-05-31
    Disaster Recovery Setup     :       infra2, 2025-06-10, 2025-06-25
    
    %% Core Development
    section Core Development
    Data Pipeline Optimization  :active,    core1, 2025-04-15, 2025-05-20
    YA Logic - CDC Fix (100%)   :crit,done, core2, 2025-05-01, 2025-05-14
    IBS Module Dev (85%)        :crit,active,core3, 2025-04-15, 2025-05-22
    Monitoring System           :active,    core4, 2025-04-15, 2025-05-25
    Post-Launch Optimizations   :           core5, 2025-06-10, 2025-06-30
    
    %% Integration
    section Integration
    TOS Integration Completion  :crit,active,int1, 2025-04-15, 2025-05-20
    API Stabilization           :active,    int2, 2025-05-15, 2025-05-30
    Production Interface Setup  :crit,      int3, 2025-05-25, 2025-06-05
    
    %% UI/UX
    section UI/UX Development
    Service KPI Dashboard       :active,    ui1, 2025-04-15, 2025-05-26
    Monitoring Dashboard        :active,    ui2, 2025-04-20, 2025-05-30
    Dashboard UI/UX Review      :crit,      ui3, 2025-05-30, 2025-06-02
    Dashboard Refinements       :           ui4, 2025-06-02, 2025-06-10
    User Guide & Documentation  :active,    ui5, 2025-05-15, 2025-06-05
    
    %% Testing & Deployment
    section Testing & Deployment
    CLT Internal Testing        :active,crit,test1, 2025-05-15, 2025-05-22
    UAT Environment Testing     :active,crit,test2, 2025-05-16, 2025-05-22
    Remote UAT by Westports     :crit,      test3, 2025-05-26, 2025-05-31
    UAT Issue Resolution        :crit,      test4, 2025-05-28, 2025-06-02
    Pre-Production Validation   :crit,      test5, 2025-06-01, 2025-06-03
    Production Deployment (Single Block) :crit, test6, 2025-06-03, 2025-06-05
    Go-Live (Single Block)      :crit,      test7, 2025-06-06, 2025-06-08
    CLT On-site Support (Week 1):crit,      test8, 2025-06-09, 2025-06-13
    CLT On-site Support (Week 2):crit,      test9, 2025-06-16, 2025-06-20
    Post-Launch Stabilization   :           test10,2025-06-20, 2025-07-10
    Performance Optimization    :           test11,2025-06-25, 2025-07-15
            </div>
        </div>

        <div class="container">
            <h2>Overall Project Status</h2>
            <div class="progress-container">
                <div class="progress-bar" style="width: 88%;">88% Complete</div>
            </div>
            <p><strong>Start Date:</strong> January 6, 2025</p>
            <p><strong>Target Go-Live Date:</strong> Early June 2025 (Targeting June 6th)</p>
            <p><strong>Current Status Date:</strong> May 14, 2025 (Week 19)</p>
            <p><strong>Progress Calculation:</strong> Based on completed work weighted by Epic importance and effort:</p>
            <ul>
                <li>Infrastructure (15% of project): 98% complete = 14.7% contribution</li>
                <li>Core Development (50% of project): ~90% complete = ~45.0% contribution (YA CDC Fix 100%, IBS Dev 85%)</li>
                <li>System Integration (20% of project): 95% complete = 19.0% contribution</li>
                <li>UI/UX Development (15% of project): 60% complete = 9.0% contribution</li>
                <li><strong>Total: ~87.7% (Rounded to 88%)</strong></li>
            </ul>
        </div>

        <div class="container">
            <h2>Module Status</h2>
            <table>
                <thead>
                    <tr>
                        <th>Module</th>
                        <th>Status</th>
                        <th>Completion</th>
                        <th>Progress</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Infrastructure Setup</td>
                        <td class="almost-done">Nearing Completion</td>
                        <td>98%</td>
                        <td><div class="progress-container"><div class="progress-bar almost-done" style="width: 98%;">98%</div></div></td>
                    </tr>
                    <tr>
                        <td>Data Pipeline Implementation</td>
                        <td class="almost-done">Nearing Completion</td>
                        <td>95%</td>
                        <td><div class="progress-container"><div class="progress-bar almost-done" style="width: 95%;">95%</div></div></td>
                    </tr>
                    <tr>
                        <td>Yard Allocation Logic (CDC Issue Fix)</td>
                        <td class="done">Complete</td>
                        <td>100%</td>
                        <td><div class="progress-container"><div class="progress-bar done" style="width: 100%;">100%</div></div></td>
                    </tr>
                    <tr>
                        <td>IBS Module (Core Dev)</td>
                        <td class="almost-done">Nearing Completion</td>
                        <td>85%</td>
                        <td><div class="progress-container"><div class="progress-bar almost-done" style="width: 85%;">85%</div></div></td>
                    </tr>
                    <tr>
                        <td>System Integration</td>
                        <td class="almost-done">Nearing Completion</td>
                        <td>95%</td>
                        <td><div class="progress-container"><div class="progress-bar almost-done" style="width: 95%;">95%</div></div></td>
                    </tr>
                    <tr>
                        <td>UI/UX Development</td>
                        <td class="active">In Progress</td>
                        <td>55%</td>
                        <td><div class="progress-container"><div class="progress-bar" style="width: 55%;">55%</div></div></td>
                    </tr>
                    <tr>
                        <td>Testing & UAT</td>
                        <td class="active">In Progress</td>
                        <td>45%</td>
                        <td><div class="progress-container"><div class="progress-bar" style="width: 45%;">45%</div></div></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="container">
            <h2>Critical Path Items (Next 2-3 Weeks, then June)</h2>
            <ul>
                <li class="critical">CLT Internal Testing Completion (Target: May 22)</li>
                <li class="critical">UAT Environment Testing & Prep (May 16-22)</li>
                <li class="critical">YA AI & IBS UAT Deployment (Target: May 21)</li>
                <li class="critical">Decision on Onsite Deployment Strategy (By May 22, now refined by CLT proposal)</li>
                <li class="critical">Dashboard (Initial) UAT Deployment (Target: May 26)</li>
                <li class="critical">Remote UAT Testing Start (Westports) (May 26)</li>
                <li class="critical">Production Environment Setup Complete (By End of May)</li>
                <li class="critical">CLT On-Site for Final UAT (June 9-13)</li>
                <li class="critical">Production Deployment (Target: June 3-5 for Go-Live June 6th)</li>
                <li class="critical">CLT On-Site for Prod Monitoring (June 16-20)</li>
            </ul>
            <p>Note: Onsite activities for CLT are proposed for June 9-13 (Final UAT) and June 16-20 (Production Monitoring & Support), supporting a Go-Live target of early June (e.g. June 6th).</p>
        </div>

        <div class="container">
            <h2>Tasks by Team Status</h2>
            <table>
                <thead>
                    <tr>
                        <th>Team</th>
                        <th>Responsibilities</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Digital Team</td>
                        <td>Leading Core Dev, Integration, UI/UX, Testing</td>
                        <td class="active">In Progress (Final internal testing, UAT prep, deployment planning)</td>
                    </tr>
                    <tr>
                        <td>TOS Team</td>
                        <td>Supporting CDC Issue Resolution (Done), UAT Testing, TOS Patch Prep</td>
                        <td class="active">In Progress (UAT preparation)</td>
                    </tr>
                    <tr>
                        <td>Deployment Team</td>
                        <td>Environment Setup, Deployment Planning, Go-Live Support</td>
                        <td class="active">In Progress (Production environment preparation)</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
