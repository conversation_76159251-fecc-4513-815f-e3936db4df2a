<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>종합 프레젠테이션 템플릿</title>
    <style>
        :root {
            --primary: #2563eb;
            --primary-light: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary: #64748b;
            --accent: #f97316;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --background: #ffffff;
            --text: #1e293b;
            --text-light: #64748b;
            --text-on-dark: #f8fafc;
            --border: #e2e8f0;
            --gradient-start: rgba(37, 99, 235, 0.1);
            --gradient-end: rgba(37, 99, 235, 0.02);
            --control-transition: all 0.3s ease;
            --font-sans: 'Pretendard', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>buntu, <PERSON><PERSON>ell, sans-serif;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: var(--font-sans);
            color: var(--text);
            background-color: #000;
            line-height: 1.5;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        /* Presentation container */
        .presentation-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            overflow: hidden;
        }

        /* Slides */
        .slides {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            background-color: var(--background);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.5s ease, visibility 0.5s;
            overflow: hidden;
        }

        .slide.active {
            opacity: 1;
            visibility: visible;
            z-index: 10;
        }

        /* Slide header */
        .slide-header {
            height: 60px;
            background-color: var(--primary);
            color: var(--text-on-dark);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            font-size: 1.2rem;
            font-weight: 500;
            z-index: 5;
        }

        .logo-area {
            display: flex;
            align-items: center;
        }

        .logo-placeholder {
            height: 36px;
            width: 120px;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            border-radius: 4px;
            margin-right: 20px;
        }

        /* Slide content */
        .slide-content {
            flex: 1;
            padding: 40px 80px 60px;
            overflow: auto;
            background-image: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        /* Slide footer */
        .slide-footer {
            height: 60px;
            background-color: var(--primary);
            color: var(--text-on-dark);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            font-size: 1rem;
            z-index: 5;
        }

        /* Progress bar */
        .progress-bar {
            height: 6px;
            background-color: rgba(255, 255, 255, 0.3);
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            z-index: 10;
        }

        .progress-indicator {
            height: 100%;
            background-color: var(--accent);
            transition: width 0.3s ease;
        }

        /* Title slide */
        .title-slide .slide-content {
            justify-content: center;
            align-items: center;
            text-align: center;
            background-image: linear-gradient(135deg, var(--gradient-start) 0%, var(--gradient-end) 100%);
        }

        .title-slide h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            color: var(--primary);
        }

        .title-slide h2 {
            font-size: 2rem;
            margin-bottom: 40px;
            color: var(--secondary);
            font-weight: 400;
        }

        .title-slide .presenter {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .title-slide .position {
            font-size: 1.2rem;
            color: var(--secondary);
            margin-bottom: 5px;
        }

        .title-slide .date {
            font-size: 1.2rem;
            color: var(--secondary);
            margin-top: 20px;
        }

        /* Section slide */
        .section-slide .slide-content {
            justify-content: center;
            align-items: center;
            background-color: var(--primary);
            color: var(--text-on-dark);
        }

        .section-slide h2 {
            font-size: 3rem;
            color: var(--text-on-dark);
            text-align: center;
            border: none;
            padding: 0;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            line-height: 1.2;
        }

        h2 {
            font-size: 2.2rem;
            margin-bottom: 25px;
            color: var(--primary);
            border-bottom: 2px solid var(--primary-light);
            padding-bottom: 10px;
        }

        h3 {
            font-size: 1.6rem;
            margin-bottom: 15px;
            margin-top: 25px;
            color: var(--primary-light);
        }

        p {
            margin-bottom: 15px;
        }

        /* Lists */
        ul, ol {
            margin-left: 25px;
            margin-bottom: 20px;
        }

        li {
            margin-bottom: 12px;
        }

        /* Layout utilities */
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 20px;
        }

        .three-column {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        /* Code block */
        .code-block {
            background-color: #1e293b;
            color: #f8fafc;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Consolas', 'Courier New', monospace;
            overflow-x: auto;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        /* Image container */
        .image-container {
            display: flex;
            justify-content: center;
            margin: 15px 0;
        }

        .image-container img {
            max-width: 100%;
            max-height: 60vh;
            object-fit: contain;
        }

        .caption {
            text-align: center;
            font-style: italic;
            color: var(--secondary);
            margin-top: 8px;
            font-size: 0.9rem;
        }

        /* Table styles */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid var(--border);
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border: 1px solid var(--border);
        }

        th {
            background-color: var(--primary);
            color: var(--text-on-dark);
            font-weight: 600;
        }

        tr:nth-child(even) {
            background-color: rgba(37, 99, 235, 0.05);
        }

        /* Card styles */
        .card {
            border: 1px solid var(--border);
            border-radius: 4px;
            padding: 20px;
            background-color: white;
            height: 100%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .card-header {
            font-weight: bold;
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: var(--primary);
            border-bottom: 1px solid var(--border);
            padding-bottom: 10px;
        }

        /* Timeline */
        .timeline {
            display: flex;
            margin: 30px 0;
        }

        .timeline-item {
            flex: 1;
            text-align: center;
            position: relative;
            padding-top: 30px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 20px;
            background-color: var(--primary);
            border-radius: 50%;
            z-index: 2;
        }

        .timeline-item::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-light);
            z-index: 1;
        }

        .timeline-item:first-child::after {
            left: 50%;
            width: 50%;
        }

        .timeline-item:last-child::after {
            width: 50%;
        }

        .timeline-date {
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--primary);
        }

        /* Process flow */
        .process-flow {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
            position: relative;
        }

        .process-step {
            width: 150px;
            text-align: center;
            z-index: 2;
        }

        .process-icon {
            width: 60px;
            height: 60px;
            background-color: var(--primary);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .process-title {
            font-weight: bold;
            margin-bottom: 5px;
            color: var(--primary);
        }

        .process-flow::after {
            content: '';
            position: absolute;
            top: 30px;
            left: 75px;
            right: 75px;
            height: 2px;
            background-color: var(--primary-light);
            z-index: 1;
        }

        /* Blockquote */
        blockquote {
            border-left: 4px solid var(--primary-light);
            padding-left: 20px;
            margin-left: 0;
            margin-bottom: 20px;
            color: var(--secondary);
            font-style: italic;
        }

        /* Highlight */
        .highlight {
            color: var(--accent);
            font-weight: bold;
        }

        /* Chart placeholder */
        .chart-placeholder {
            width: 100%;
            height: 300px;
            background-color: rgba(37, 99, 235, 0.05);
            border: 1px dashed var(--border);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--secondary);
            font-style: italic;
            margin-bottom: 20px;
        }

        /* Navigation controls */
        .controls,
        .control-btn {
            display: none;
        }

        .presentation-container:hover .controls,
        .controls:hover,
        .controls.active {
            display: none;
        }

        /* Slide counter */
        .slide-counter {
            position: fixed;
            bottom: 15px;
            right: 15px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            z-index: 100;
            opacity: 0;
            transition: var(--control-transition);
        }

        .presentation-container:hover .slide-counter,
        .slide-counter:hover {
            opacity: 1;
        }

        /* Fullscreen button */
        .fullscreen-btn {
            position: fixed;
            top: 15px;
            right: 15px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            transition: var(--control-transition);
            opacity: 0;
        }

        .presentation-container:hover .fullscreen-btn,
        .fullscreen-btn:hover {
            opacity: 1;
        }

        /* Slide navigation hints */
        .navigation-hint {
            position: fixed;
            bottom: 50%;
            transform: translateY(50%);
            background-color: rgba(0, 0, 0, 0.2);
            color: white;
            border: none;
            width: 30px;
            height: 60px;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            z-index: 90;
            opacity: 0;
            transition: var(--control-transition);
        }

        .navigation-hint.prev {
            left: 10px;
        }

        .navigation-hint.next {
            right: 10px;
        }

        .presentation-container:hover .navigation-hint {
            opacity: 0.5;
        }

        .navigation-hint:hover {
            opacity: 0.8 !important;
            cursor: pointer;
            background-color: rgba(0, 0, 0, 0.4);
        }

        /* Slide overview */
        .slide-overview {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            display: none;
            flex-wrap: wrap;
            justify-content: center;
            align-content: flex-start;
            padding: 20px;
            gap: 20px;
            overflow-y: auto;
            z-index: 1000;
        }

        .slide-overview.active {
            display: flex;
        }

        .slide-thumbnail {
            width: 300px;
            height: 180px;
            background-color: white;
            border: 2px solid transparent;
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s, border-color 0.2s;
            transform: scale(0.95);
            position: relative;
        }

        .slide-thumbnail:hover {
            transform: scale(1);
            border-color: var(--primary);
        }

        .slide-thumbnail.current {
            border-color: var(--accent);
            transform: scale(1);
        }

        .slide-thumbnail-number {
            position: absolute;
            bottom: 5px;
            right: 5px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7rem;
        }

        /* Show controls briefly when slide changes */
        .controls.briefly-visible,
        .slide-counter.briefly-visible {
            opacity: 1;
            pointer-events: all;
        }

        /* Comparison table */
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: center;
            border: 1px solid var(--border);
        }

        .comparison-table th:first-child,
        .comparison-table td:first-child {
            text-align: left;
            font-weight: bold;
        }

        .comparison-table th {
            background-color: var(--primary);
            color: white;
        }

        .comparison-table tr:nth-child(even) {
            background-color: rgba(37, 99, 235, 0.05);
        }

        /* Feature comparison */
        .feature-comparison {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin: 30px 0;
        }

        .feature-column {
            border: 1px solid var(--border);
            border-radius: 8px;
            overflow: hidden;
        }

        .feature-header {
            background-color: var(--primary);
            color: white;
            padding: 15px;
            text-align: center;
            font-size: 1.4rem;
            font-weight: bold;
        }

        .feature-content {
            padding: 20px;
        }

        .feature-list {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }

        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: '✓';
            color: var(--success);
            margin-right: 10px;
            font-weight: bold;
        }

        /* Image with text */
        .image-text-container {
            display: flex;
            align-items: center;
            gap: 30px;
            margin: 20px 0;
        }

        .image-text-container.image-left {
            flex-direction: row;
        }

        .image-text-container.image-right {
            flex-direction: row-reverse;
        }

        .image-text-container .image {
            flex: 1;
            display: flex;
            justify-content: center;
        }

        .image-text-container .text {
            flex: 1;
        }

        .image-text-container img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Agenda list */
        .agenda-list {
            list-style-type: none;
            counter-reset: agenda;
            margin: 0;
            padding: 0;
        }

        .agenda-list li {
            counter-increment: agenda;
            padding: 15px 0;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
        }

        .agenda-list li:last-child {
            border-bottom: none;
        }

        .agenda-list li::before {
            content: counter(agenda);
            background-color: var(--primary);
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
        }

        /* Chart container */
        .chart-container {
            width: 100%;
            height: 400px;
            margin: 20px 0;
        }

        /* Summary boxes */
        .summary-boxes {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .summary-box {
            background-color: rgba(37, 99, 235, 0.05);
            border-left: 4px solid var(--primary);
            padding: 20px;
            border-radius: 4px;
        }

        .summary-box h3 {
            margin-top: 0;
            color: var(--primary);
        }

        /* Numbered header */
        .numbered-header {
            display: flex;
            align-items: center;
            gap: 2px;
            margin-bottom: 5px;
        }

        .numbered-header .number {
            font-size: 2.2rem;
            font-weight: bold;
            color: var(--primary);
        }

        .numbered-header h1 {
            margin: 0;
            font-size: 1.7rem;
            color: var(--text);
        }

        .numbered-header h2 {
            margin: 0 0 0 5px;
            font-size: 1.1rem;
            color: var(--text-light);
            font-weight: normal;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .slide-header, .slide-footer {
                padding: 0 20px;
            }

            .slide-content {
                padding: 20px;
            }

            .two-column, .three-column, .feature-comparison, .summary-boxes {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .process-flow {
                flex-direction: column;
                align-items: center;
                gap: 30px;
            }

            .process-flow::after {
                display: none;
            }

            .timeline {
                flex-direction: column;
                gap: 30px;
            }

            .timeline-item::after {
                display: none;
            }

            .title-slide h1 {
                font-size: 2.5rem;
            }

            .title-slide h2 {
                font-size: 1.5rem;
            }

            .controls {
                bottom: 20px;
            }

            .image-text-container {
                flex-direction: column !important;
            }

            .numbered-header h1 {
                font-size: 2rem;
                margin-left: 5rem;
            }

            .numbered-header h2 {
                font-size: 1.2rem;
                margin-left: 5rem;
            }
            
            /* Always show controls on touch devices */
            .controls, 
            .slide-counter,
            .fullscreen-btn {
                opacity: 0.7;
                pointer-events: all;
            }
        }

        /* Print styles */
        @media print {
            body {
                background-color: white;
            }

            .slide-wrapper {
                box-shadow: none;
                page-break-after: always;
                margin: 0;
            }

            .slide {
                opacity: 1 !important;
                visibility: visible !important;
                position: relative;
                page-break-after: always;
                height: 100vh;
            }

            .controls, .fullscreen-btn, .slide-counter, .slide-overview, .navigation-hint {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="slides">
            <!-- 1. 제목 슬라이드 -->
            <div class="slide title-slide active" id="slide-1">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div></div>
                </div>
                <div class="slide-content">
                    <h1>종합 프레젠테이션 템플릿</h1>
                    <h2>다양한 레이아웃을 포함한 단일 HTML 파일</h2>
                    <p class="presenter">홍길동</p>
                    <p class="position">수석 프로젝트 매니저</p>
                    <p class="position">기술 솔루션 부서</p>
                    <p class="date">2025년 5월 15일</p>
                </div>
                <div class="slide-footer">
                    <div>기밀 문서</div>
                    <div>1 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 10%;"></div>
                </div>
            </div>

            <!-- 2. 목차/아젠다 슬라이드 -->
            <div class="slide" id="slide-2">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>목차</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">01</span>
                        <h1>프레젠테이션 목차</h1>
                    </div>

                    <ul class="agenda-list">
                        <li>프로젝트 개요 및 목표</li>
                        <li>현재 상황 분석
                            <ul>
                                <li>시장 동향</li>
                                <li>경쟁사 분석</li>
                                <li>내부 역량 평가</li>
                            </ul>
                        </li>
                        <li>제안 솔루션
                            <ul>
                                <li>기술적 접근 방식</li>
                                <li>주요 기능 및 특징</li>
                                <li>구현 방법론</li>
                            </ul>
                        </li>
                        <li>프로젝트 일정 및 마일스톤</li>
                        <li>비용 분석 및 ROI</li>
                        <li>다음 단계 및 논의 사항</li>
                    </ul>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>2 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 20%;"></div>
                </div>
            </div>

            <!-- 3. 2단 분할 레이아웃 -->
            <div class="slide" id="slide-3">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>2단 분할 레이아웃</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">02</span>
                        <h1>2단 분할 레이아웃</h1>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>왼쪽 컬럼</h3>
                            <p>2단 분할 레이아웃은 관련된 내용을 나란히 표시하기에 적합합니다. 이 레이아웃은 다음과 같은 용도로 활용할 수 있습니다:</p>
                            <ul>
                                <li>개념 비교</li>
                                <li>전/후 예시 보여주기</li>
                                <li>텍스트와 이미지 함께 표시</li>
                                <li>문제와 해결책 대비</li>
                                <li>장점과 단점 분석</li>
                            </ul>
                            <p>각 컬럼은 동일한 너비를 가지며, 반응형으로 설계되어 화면 크기에 따라 자동으로 조정됩니다.</p>
                        </div>
                        <div>
                            <h3>오른쪽 컬럼</h3>
                            <blockquote>
                                "좋은 디자인은 최소한의 디자인입니다. 적을수록 좋습니다 - 필수적인 요소에 집중하기 때문입니다."<br>
                                — 디터 람스
                            </blockquote>
                            <div class="image-container">
                                <img src="https://via.placeholder.com/500x300?text=2단+레이아웃+예시" alt="2단 레이아웃 예시">
                            </div>
                            <p class="caption">그림 1: 2단 레이아웃 예시</p>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>3 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 30%;"></div>
                </div>
            </div>

            <!-- 4. 3단 분할 레이아웃 -->
            <div class="slide" id="slide-4">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>3단 분할 레이아웃</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">03</span>
                        <h1>3단 그리드 레이아웃</h1>
                    </div>

                    <div class="three-column">
                        <div class="card">
                            <div class="card-header">기본 플랜</div>
                            <ul>
                                <li>핵심 기능 접근</li>
                                <li>기본 분석 도구</li>
                                <li>이메일 지원</li>
                                <li>월 5GB 스토리지</li>
                                <li>최대 3명 사용자</li>
                            </ul>
                            <div class="image-container">
                                <img src="https://via.placeholder.com/150x100?text=기본+플랜" alt="기본 플랜">
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">프로 플랜</div>
                            <ul>
                                <li>모든 기본 플랜 기능</li>
                                <li>고급 분석 도구</li>
                                <li>우선 지원</li>
                                <li>월 20GB 스토리지</li>
                                <li>최대 10명 사용자</li>
                            </ul>
                            <div class="image-container">
                                <img src="https://via.placeholder.com/150x100?text=프로+플랜" alt="프로 플랜">
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">엔터프라이즈 플랜</div>
                            <ul>
                                <li>모든 프로 플랜 기능</li>
                                <li>맞춤형 분석 솔루션</li>
                                <li>24/7 전담 지원</li>
                                <li>무제한 스토리지</li>
                                <li>무제한 사용자</li>
                            </ul>
                            <div class="image-container">
                                <img src="https://via.placeholder.com/150x100?text=엔터프라이즈+플랜" alt="엔터프라이즈 플랜">
                            </div>
                        </div>
                    </div>

                    <p>3단 레이아웃은 여러 항목을 나란히 비교할 때 특히 유용합니다. 가격 책정 플랜, 제품 비교, 서비스 패키지 등을 표시하는 데 적합합니다.</p>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>4 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 40%;"></div>
                </div>
            </div>

            <!-- 5. 이미지와 텍스트 조합 -->
            <div class="slide" id="slide-5">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>이미지와 텍스트 조합</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">04</span>
                        <h1>이미지와 텍스트 조합</h1>
                    </div>

                    <div class="image-text-container image-left">
                        <div class="image">
                            <img src="https://via.placeholder.com/500x300?text=왼쪽+이미지+예시" alt="왼쪽 이미지 예시">
                        </div>
                        <div class="text">
                            <h3>왼쪽 이미지 레이아웃</h3>
                            <p>이미지와 텍스트를 조합한 레이아웃은 시각적 요소와 설명을 효과적으로 결합할 수 있습니다. 이 예시에서는 이미지가 왼쪽에 배치되어 있습니다.</p>
                            <p>이러한 레이아웃은 다음과 같은 용도로 활용할 수 있습니다:</p>
                            <ul>
                                <li>제품 소개 및 특징 설명</li>
                                <li>개념 시각화 및 설명</li>
                                <li>사례 연구 및 결과 표시</li>
                            </ul>
                        </div>
                    </div>

                    <div class="image-text-container image-right">
                        <div class="image">
                            <img src="https://via.placeholder.com/500x300?text=오른쪽+이미지+예시" alt="오른쪽 이미지 예시">
                        </div>
                        <div class="text">
                            <h3>오른쪽 이미지 레이아웃</h3>
                            <p>이 예시에서는 이미지가 오른쪽에 배치되어 있습니다. 이미지 위치를 바꾸면 시각적 흐름을 다양하게 만들 수 있습니다.</p>
                            <p>이미지 위치는 내용의 중요도와 읽기 흐름에 따라 선택할 수 있습니다. 일반적으로 서양 문화에서는 왼쪽에서 오른쪽으로 읽기 때문에, 먼저 주목해야 할 요소를 왼쪽에 배치하는 경우가 많습니다.</p>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>5 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 50%;"></div>
                </div>
            </div>

            <!-- 6. 비교/대조 레이아웃 -->
            <div class="slide" id="slide-6">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>비교/대조 레이아웃</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">05</span>
                        <h1>비교 테이블</h1>
                    </div>

                    <h3>기능 비교표</h3>
                    <table class="comparison-table">
                        <thead>
                            <tr>
                                <th>기능</th>
                                <th>기본 플랜</th>
                                <th>프로 플랜</th>
                                <th>엔터프라이즈 플랜</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>사용자 수</td>
                                <td>최대 3명</td>
                                <td>최대 10명</td>
                                <td>무제한</td>
                            </tr>
                            <tr>
                                <td>스토리지</td>
                                <td>5GB</td>
                                <td>20GB</td>
                                <td>무제한</td>
                            </tr>
                            <tr>
                                <td>API 접근</td>
                                <td>❌</td>
                                <td>✓</td>
                                <td>✓</td>
                            </tr>
                            <tr>
                                <td>고급 분석</td>
                                <td>❌</td>
                                <td>✓</td>
                                <td>✓</td>
                            </tr>
                            <tr>
                                <td>맞춤형 보고서</td>
                                <td>❌</td>
                                <td>❌</td>
                                <td>✓</td>
                            </tr>
                            <tr>
                                <td>지원 수준</td>
                                <td>이메일</td>
                                <td>우선 지원</td>
                                <td>24/7 전담 지원</td>
                            </tr>
                        </tbody>
                    </table>

                    <h3>솔루션 비교</h3>
                    <div class="feature-comparison">
                        <div class="feature-column">
                            <div class="feature-header">현재 시스템</div>
                            <div class="feature-content">
                                <ul class="feature-list">
                                    <li>레거시 모놀리식 아키텍처</li>
                                    <li>제한된 확장성</li>
                                    <li>높은 유지보수 비용</li>
                                    <li>느린 배포 주기 (2-3주)</li>
                                    <li>제한된 통합 옵션</li>
                                    <li>온프레미스 인프라</li>
                                </ul>
                            </div>
                        </div>
                        <div class="feature-column">
                            <div class="feature-header">제안 솔루션</div>
                            <div class="feature-content">
                                <ul class="feature-list">
                                    <li>마이크로서비스 아키텍처</li>
                                    <li>클라우드 네이티브 인프라</li>
                                    <li>CI/CD 자동화</li>
                                    <li>단계적 마이그레이션</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>6 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 60%;"></div>
                </div>
            </div>

            <!-- 7. 타임라인/프로세스 흐름도 -->
            <div class="slide" id="slide-7">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>타임라인/프로세스 흐름도</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">06</span>
                        <h1>프로세스 플로우</h1>
                    </div>

                    <h3>프로젝트 타임라인</h3>
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-date">2025년 3분기</div>
                            <div>프로젝트 시작</div>
                            <ul>
                                <li>요구사항 확정</li>
                                <li>팀 구성</li>
                                <li>인프라 설정</li>
                            </ul>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2025년 4분기</div>
                            <div>1단계 구현</div>
                            <ul>
                                <li>핵심 서비스 마이그레이션</li>
                                <li>API 게이트웨이 구현</li>
                                <li>CI/CD 파이프라인 설정</li>
                            </ul>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2026년 1분기</div>
                            <div>2단계 구현</div>
                            <ul>
                                <li>고객 대면 서비스</li>
                                <li>데이터 마이그레이션 완료</li>
                                <li>레거시 시스템 통합</li>
                            </ul>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-date">2026년 2분기</div>
                            <div>최종 구현</div>
                            <ul>
                                <li>전체 시스템 전환</li>
                                <li>성능 최적화</li>
                                <li>레거시 시스템 폐기</li>
                            </ul>
                        </div>
                    </div>

                    <h3>구현 접근 방식</h3>
                    <div class="process-flow">
                        <div class="process-step">
                            <div class="process-icon">1</div>
                            <div class="process-title">평가</div>
                            <div>시스템 감사 및 요구사항 수집</div>
                        </div>
                        <div class="process-step">
                            <div class="process-icon">2</div>
                            <div class="process-title">설계</div>
                            <div>아키텍처 및 서비스 경계 정의</div>
                        </div>
                        <div class="process-step">
                            <div class="process-icon">3</div>
                            <div class="process-title">개발</div>
                            <div>반복적 개발 및 테스트</div>
                        </div>
                        <div class="process-step">
                            <div class="process-icon">4</div>
                            <div class="process-title">배포</div>
                            <div>단계적 마이그레이션 및 출시</div>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>7 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 70%;"></div>
                </div>
            </div>

            <!-- 8. 데이터/차트 중심 레이아웃 -->
            <div class="slide" id="slide-8">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>데이터/차트 중심 레이아웃</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">07</span>
                        <h1>데이터/차트 중심 레이아웃</h1>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>성능 지표</h3>
                            <table>
                                <thead>
                                    <tr>
                                        <th>지표</th>
                                        <th>현재 값</th>
                                        <th>목표 값</th>
                                        <th>차이</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>평균 응답 시간</td>
                                        <td>3.2초</td>
                                        <td>< 1초</td>
                                        <td class="highlight">320% 초과</td>
                                    </tr>
                                    <tr>
                                        <td>시스템 가용성</td>
                                        <td>97.2%</td>
                                        <td>99.9%</td>
                                        <td>2.7% 미달</td>
                                    </tr>
                                    <tr>
                                        <td>트랜잭션 처리량</td>
                                        <td>120 TPS</td>
                                        <td>500+ TPS</td>
                                        <td>76% 미달</td>
                                    </tr>
                                    <tr>
                                        <td>데이터베이스 쿼리 시간</td>
                                        <td>1.8초</td>
                                        <td>< 0.5초</td>
                                        <td>360% 초과</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h3>주요 발견 사항</h3>
                            <ul>
                                <li>현재 시스템은 <span class="highlight">피크 시간대에 심각한 성능 저하</span>를 보임</li>
                                <li>데이터베이스 쿼리가 병목 현상의 주요 원인</li>
                                <li>확장성 제한으로 인해 사용자 증가에 따른 대응 어려움</li>
                                <li>레거시 코드베이스로 인한 유지보수 복잡성 증가</li>
                            </ul>
                        </div>
                        <div>
                            <h3>월별 성능 추이</h3>
                            <div class="chart-placeholder">
                                월별 성능 추이 차트 (막대 그래프)
                            </div>

                            <h3>사용자 만족도</h3>
                            <div class="chart-placeholder">
                                사용자 만족도 차트 (원형 그래프)
                            </div>
                        </div>
                    </div>

                    <h3>예상 개선 효과</h3>
                    <div class="chart-placeholder">
                        현재 시스템과 제안 시스템의 성능 비교 차트 (막대 그래프)
                    </div>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>8 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 80%;"></div>
                </div>
            </div>

            <!-- 9. 마무리/요약 슬라이드 -->
            <div class="slide" id="slide-9">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>마무리/요약</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">08</span>
                        <h1>요약 박스</h1>
                    </div>

                    <h3>주요 요점 요약</h3>
                    <div class="summary-boxes">
                        <div class="summary-box">
                            <h3>현재 상황</h3>
                            <ul>
                                <li>레거시 시스템 성능 저하</li>
                                <li>유지보수 비용 증가</li>
                                <li>확장성 제한</li>
                                <li>고객 만족도 하락</li>
                            </ul>
                        </div>
                        <div class="summary-box">
                            <h3>제안 솔루션</h3>
                            <ul>
                                <li>마이크로서비스 아키텍처</li>
                                <li>클라우드 네이티브 인프라</li>
                                <li>CI/CD 자동화</li>
                                <li>단계적 마이그레이션</li>
                            </ul>
                        </div>
                        <div class="summary-box">
                            <h3>기대 효과</h3>
                            <ul>
                                <li>성능 300% 향상</li>
                                <li>운영 비용 40% 절감</li>
                                <li>배포 시간 95% 단축</li>
                                <li>고객 만족도 증가</li>
                            </ul>
                        </div>
                    </div>

                    <h3>다음 단계</h3>
                    <div class="two-column">
                        <div>
                            <ol>
                                <li>아키텍처 팀과 기술 심층 논의</li>
                                <li>범위 및 요구사항 문서 확정</li>
                                <li>리소스 할당 및 팀 구성</li>
                                <li>프로젝트 킥오프 미팅 (제안: 2025년 6월 1일)</li>
                                <li>필요한 인프라 자원 조달</li>
                            </ol>
                        </div>
                        <div>
                            <blockquote>
                                "이 제안은 귀사의 비즈니스 성장을 수년간 지원할 수 있는 확장 가능하고, 유지보수가 용이하며, 미래 지향적인 솔루션을 제공하겠다는 우리의 약속을 나타냅니다."
                            </blockquote>
                            <div class="image-container">
                                <img src="https://via.placeholder.com/300x200?text=팀+사진" alt="팀 사진">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>9 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 90%;"></div>
                </div>
            </div>

            <!-- 10. 연락처 슬라이드 -->
            <div class="slide" id="slide-10">
                <div class="slide-header">
                    <div class="logo-area">
                        <div class="logo-placeholder">COMPANY LOGO</div>
                    </div>
                    <div>연락처</div>
                </div>
                <div class="slide-content">
                    <div class="numbered-header">
                        <span class="number">09</span>
                        <h1>연락처 정보</h1>
                        <h2>질문이나 추가 정보가 필요하신가요?</h2>
                    </div>

                    <div class="two-column">
                        <div>
                            <h3>연락처 정보</h3>
                            <div class="card" style="padding: 20px;">
                                <div style="text-align: center; margin-bottom: 20px;">
                                    <div class="image-container" style="margin: 0 auto 15px;">
                                        <img src="https://via.placeholder.com/150x150?text=홍길동" alt="홍길동 사진" style="border-radius: 50%;">
                                    </div>
                                    <h3 style="margin-bottom: 5px;">홍길동</h3>
                                    <p>수석 프로젝트 매니저</p>
                                    <p>기술 솔루션 부서</p>
                                </div>
                                <div style="margin-top: 20px;">
                                    <p><strong>이메일:</strong> <EMAIL></p>
                                    <p><strong>전화:</strong> +82 10-1234-5678</p>
                                    <p><strong>사무실:</strong> 본사 12층</p>
                                    <p><strong>주소:</strong> 서울특별시 강남구 테헤란로 123</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3>질문이 있으신가요?</h3>
                            <p>추가 정보가 필요하시거나 질문이 있으시면 언제든지 연락해 주세요. 귀하의 비즈니스 요구 사항을 충족시키기 위한 맞춤형 솔루션을 제공해 드리겠습니다.</p>
                            
                            <h3>다음 단계</h3>
                            <ul>
                                <li>상세 제안서 요청</li>
                                <li>기술 데모 일정 잡기</li>
                                <li>팀 미팅 예약</li>
                                <li>맞춤형 견적 요청</li>
                            </ul>
                            
                            <blockquote style="margin-top: 30px;">
                                "혁신은 변화를 두려워하지 않는 사람들로부터 시작됩니다. 함께 미래를 만들어 나가요."
                            </blockquote>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 40px;">
                        <h3>감사합니다</h3>
                        <p>귀중한 시간을 내어 저희 프레젠테이션을 봐주셔서 감사합니다.</p>
                    </div>
                </div>
                <div class="slide-footer">
                    <div>종합 프레젠테이션 템플릿</div>
                    <div>10 / 10</div>
                </div>
                <div class="progress-bar">
                    <div class="progress-indicator" style="width: 100%;"></div>
                </div>
            </div>
        </div>

        <!-- Side Navigation Hints - New feature -->
        <div class="navigation-hint prev" id="prev-hint">←</div>
        <div class="navigation-hint next" id="next-hint">→</div>

        <!-- Slide Counter - Now hidden by default -->
        <div class="slide-counter" id="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">10</span>
        </div>

        <!-- Fullscreen Button - Now hidden by default -->
        <button class="fullscreen-btn" id="fullscreen-btn">⛶</button>

        <!-- Slide Overview -->
        <div class="slide-overview" id="slide-overview">
            <!-- Thumbnails will be generated by JavaScript -->
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const slides = document.querySelectorAll('.slide');
            const prevHint = document.getElementById('prev-hint');
            const nextHint = document.getElementById('next-hint');
            const fullscreenBtn = document.getElementById('fullscreen-btn');
            const slideCounter = document.getElementById('current-slide');
            const totalSlides = document.getElementById('total-slides');
            const slideOverview = document.getElementById('slide-overview');
            
            // Variables
            let currentSlide = 0;
            let isOverviewActive = false;
            
            // Initialize
            totalSlides.textContent = slides.length;
            
            // Function to update button states
            function updateButtons() {
                prevHint.style.display = currentSlide === 0 ? 'none' : 'flex';
                nextHint.style.display = currentSlide === slides.length - 1 ? 'none' : 'flex';
                slideCounter.textContent = currentSlide + 1;
            }
            
            // Function to show a specific slide
            function showSlide(index) {
                if (index < 0 || index >= slides.length) return;
                
                slides[currentSlide].classList.remove('active');
                currentSlide = index;
                slides[currentSlide].classList.add('active');
                updateButtons();
            }
            
            // Event listeners for navigation hints
            prevHint.addEventListener('click', function() {
                if (currentSlide > 0) {
                    showSlide(currentSlide - 1);
                }
            });
            
            nextHint.addEventListener('click', function() {
                if (currentSlide < slides.length - 1) {
                    showSlide(currentSlide + 1);
                }
            });
            
            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (isOverviewActive) {
                    if (e.key === 'Escape') {
                        toggleOverview();
                    }
                    return;
                }
                
                if (e.key === 'ArrowRight' || e.key === ' ' || e.key === 'PageDown') {
                    if (currentSlide < slides.length - 1) {
                        showSlide(currentSlide + 1);
                    }
                } else if (e.key === 'ArrowLeft' || e.key === 'PageUp') {
                    if (currentSlide > 0) {
                        showSlide(currentSlide - 1);
                    }
                } else if (e.key === 'Home') {
                    showSlide(0);
                } else if (e.key === 'End') {
                    showSlide(slides.length - 1);
                } else if (e.key === 'o') {
                    toggleOverview();
                } else if (e.key === 'f') {
                    toggleFullscreen();
                }
            });
            
            // Fullscreen toggle
            function toggleFullscreen() {
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen().catch(err => {
                        console.log(`Error attempting to enable fullscreen: ${err.message}`);
                    });
                } else {
                    if (document.exitFullscreen) {
                        document.exitFullscreen();
                    }
                }
            }
            
            fullscreenBtn.addEventListener('click', toggleFullscreen);
            
            // Slide overview
            function createOverview() {
                slideOverview.innerHTML = '';
                
                slides.forEach((slide, index) => {
                    const thumbnail = document.createElement('div');
                    thumbnail.className = 'slide-thumbnail';
                    if (index === currentSlide) {
                        thumbnail.classList.add('current');
                    }
                    
                    // Clone the slide and clear its content
                    const content = slide.cloneNode(true);
                    // Directly clear the slide content section
                    const slideContent = content.querySelector('.slide-content');
                    if (slideContent) {
                        slideContent.innerHTML = '';
                    }
                    content.style.transform = 'scale(0.2)';
                    content.style.transformOrigin = 'top left';
                    content.style.width = '500%';
                    content.style.height = '500%';
                    content.style.position = 'absolute';
                    content.style.top = '0';
                    content.style.left = '0';
                    content.style.opacity = '1';
                    content.style.visibility = 'visible';
                    
                    // Add slide number
                    const number = document.createElement('div');
                    number.className = 'slide-thumbnail-number';
                    number.textContent = index + 1;
                    
                    thumbnail.appendChild(content);
                    thumbnail.appendChild(number);
                    
                    thumbnail.addEventListener('click', () => {
                        showSlide(index);
                        toggleOverview();
                    });
                    
                    slideOverview.appendChild(thumbnail);
                });
            }
            
            function toggleOverview() {
                if (!isOverviewActive) {
                    createOverview();
                    slideOverview.classList.add('active');
                } else {
                    slideOverview.classList.remove('active');
                }
                isOverviewActive = !isOverviewActive;
            }
            
            // Double click on slide to enter overview
            document.querySelector('.slides').addEventListener('dblclick', function(e) {
                if (!isOverviewActive) {
                    toggleOverview();
                }
            });
            
            // Touch events for mobile
            let touchStartX = 0;
            let touchEndX = 0;
            
            document.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            });
            
            document.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });
            
            function handleSwipe() {
                const threshold = 50;
                if (touchEndX < touchStartX - threshold) {
                    // Swipe left
                    if (currentSlide < slides.length - 1) {
                        showSlide(currentSlide + 1);
                    }
                } else if (touchEndX > touchStartX + threshold) {
                    // Swipe right
                    if (currentSlide > 0) {
                        showSlide(currentSlide - 1);
                    }
                }
            }
            
            // Initialize
            updateButtons();
        });
    </script>
</body>
</html>