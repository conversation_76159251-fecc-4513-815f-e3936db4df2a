#!/bin/bash

# 색상 정의
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 출력 파일 정의
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_FILE="system_info_${TIMESTAMP}.txt"
ERROR_FILE="system_info_errors_${TIMESTAMP}.txt"

# 헤더 출력 함수
print_header() {
    echo -e "${BLUE}===== $1 =====${NC}" | tee -a $OUTPUT_FILE
    echo "" | tee -a $OUTPUT_FILE
}

# 명령 실행 함수 (에러 처리 포함)
run_command() {
    local description=$1
    local command=$2
    echo -e "${YELLOW}[실행중] ${description}${NC}"
    echo "### ${description} ###" >> $OUTPUT_FILE
    echo "명령어: ${command}" >> $OUTPUT_FILE
    echo "---" >> $OUTPUT_FILE
    eval "${command}" >> $OUTPUT_FILE 2>> $ERROR_FILE
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}[완료] ${description}${NC}"
    else
        echo -e "${RED}[실패] ${description} (에러 코드: $exit_code)${NC}"
        echo "에러 발생: ${description} (exit code: $exit_code)" >> $ERROR_FILE
    fi
    echo "" >> $OUTPUT_FILE
    return $exit_code
}

# 스크립트 시작
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  Ubuntu ARM 시스템 정보 수집 스크립트  ${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""
echo "수집 시작: $(date)" | tee $OUTPUT_FILE
echo "" | tee -a $OUTPUT_FILE

# 1. 기본 시스템 정보
print_header "기본 시스템 정보"
run_command "시스템 아키텍처" "uname -a"
run_command "OS 버전" "lsb_release -a 2>/dev/null || cat /etc/os-release"
run_command "CPU 정보" "lscpu"
run_command "메모리 정보" "free -h"
run_command "디스크 사용량" "df -h"
run_command "블록 디바이스 정보" "lsblk"

# 2. 네트워크 설정
print_header "네트워크 설정"
run_command "네트워크 인터페이스" "ip a"
run_command "라우팅 테이블" "ip route"
run_command "DNS 설정" "cat /etc/resolv.conf"
run_command "네트워크 연결 상태" "ss -tuln | head -20"

# 3. 마운트 정보 (OneDrive 관련 집중)
print_header "마운트 및 파일시스템 정보"
run_command "전체 마운트 정보" "mount -v"
run_command "파일시스템 타입별 마운트" "findmnt -t fuse,fuse.rclone,cifs,nfs"
run_command "OneDrive 관련 마운트 검색" "mount | grep -i 'onedrive\|rclone\|fuse'"
run_command "fstab 내용" "cat /etc/fstab"
run_command "systemd 마운트 유닛" "systemctl list-units --type=mount"

# 4. OneDrive/rclone 특정 정보
print_header "OneDrive/rclone 설정 확인"
run_command "rclone 버전" "rclone version 2>/dev/null || echo 'rclone not found'"
run_command "rclone 설정 위치" "rclone config file 2>/dev/null || echo 'rclone not found'"
run_command "rclone 리모트 목록" "rclone listremotes 2>/dev/null || echo 'rclone not found'"

# OneDrive 마운트 포인트 찾기 및 검사
echo "### OneDrive 마운트 포인트 검색 ###" >> $OUTPUT_FILE
ONEDRIVE_MOUNTS=$(mount | grep -i 'onedrive\|rclone' | awk '{print $3}')
if [ -n "$ONEDRIVE_MOUNTS" ]; then
    echo "발견된 OneDrive 마운트 포인트:" >> $OUTPUT_FILE
    echo "$ONEDRIVE_MOUNTS" >> $OUTPUT_FILE
    
    for mount_point in $ONEDRIVE_MOUNTS; do
        echo "" >> $OUTPUT_FILE
        echo "=== 마운트 포인트: $mount_point ===" >> $OUTPUT_FILE
        run_command "디렉토리 권한" "ls -ld '$mount_point'"
        run_command "상위 10개 항목" "ls -la '$mount_point' | head -10"
        run_command "접근 권한 테스트" "test -r '$mount_point' && echo '읽기 가능' || echo '읽기 불가'"
        run_command "쓰기 권한 테스트" "test -w '$mount_point' && echo '쓰기 가능' || echo '쓰기 불가'"
        run_command "실행 권한 테스트" "test -x '$mount_point' && echo '실행 가능' || echo '실행 불가'"
    done
else
    echo "OneDrive 마운트 포인트를 찾을 수 없습니다." >> $OUTPUT_FILE
fi

# 5. 프로세스 정보
print_header "관련 프로세스 정보"
run_command "rsync 프로세스" "ps aux | grep -E 'rsync|rclone' | grep -v grep"
run_command "FUSE 관련 프로세스" "ps aux | grep -i fuse | grep -v grep"
run_command "마운트 관련 프로세스" "ps aux | grep -E 'mount|onedrive' | grep -v grep"

# 6. 사용자 및 권한 정보
print_header "사용자 및 권한 정보"
run_command "현재 사용자 정보" "id"
run_command "사용자 그룹" "groups"
run_command "sudo 권한" "sudo -l 2>/dev/null || echo 'sudo 권한 확인 불가'"

# 7. 시스템 로그 확인
print_header "시스템 로그 (최근 OneDrive/rsync 관련)"
run_command "시스템 로그 - rsync" "journalctl -xe | grep -i rsync | tail -20 2>/dev/null || dmesg | grep -i rsync | tail -20"
run_command "시스템 로그 - rclone" "journalctl -xe | grep -i rclone | tail -20 2>/dev/null || dmesg | grep -i rclone | tail -20"
run_command "시스템 로그 - mount" "journalctl -xe | grep -i 'mount\|fuse' | tail -20 2>/dev/null || dmesg | grep -i 'mount\|fuse' | tail -20"

# 8. rsync 설정 파일 확인
print_header "rsync 설정 파일"
run_command "rsync 설정 파일" "ls -la /etc/rsync* 2>/dev/null || echo '설정 파일 없음'"
run_command "사용자 rsync 설정" "ls -la ~/.rsync* 2>/dev/null || echo '사용자 설정 없음'"

# 9. SELinux/AppArmor 상태 확인
print_header "보안 모듈 상태"
run_command "SELinux 상태" "getenforce 2>/dev/null || echo 'SELinux 미설치'"
run_command "AppArmor 상태" "aa-status 2>/dev/null || systemctl status apparmor 2>/dev/null || echo 'AppArmor 미설치'"

# 10. 커널 모듈 확인
print_header "커널 모듈"
run_command "FUSE 모듈" "lsmod | grep fuse"
run_command "로드된 파일시스템 모듈" "cat /proc/filesystems"

# 완료 메시지
echo "" | tee -a $OUTPUT_FILE
echo "========================================" | tee -a $OUTPUT_FILE
echo "수집 완료: $(date)" | tee -a $OUTPUT_FILE
echo "========================================" | tee -a $OUTPUT_FILE

echo ""
echo -e "${GREEN}정보 수집이 완료되었습니다!${NC}"
echo -e "${BLUE}결과 파일:${NC} $OUTPUT_FILE"
echo -e "${BLUE}에러 파일:${NC} $ERROR_FILE"
echo ""
echo -e "${YELLOW}다음 명령으로 결과를 확인하세요:${NC}"
echo "  cat $OUTPUT_FILE"
echo ""
echo -e "${YELLOW}주요 문제점을 빠르게 확인하려면:${NC}"
echo "  grep -E 'error|fail|denied|permission' $OUTPUT_FILE"
