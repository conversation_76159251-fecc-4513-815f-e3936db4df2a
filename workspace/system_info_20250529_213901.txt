수집 시작: Thu May 29 21:39:01 UTC 2025

[0;34m===== 기본 시스템 정보 =====[0m

### 시스템 아키텍처 ###
명령어: uname -a
---
Linux instance-************* 6.8.0-1024-oracle #25-Ubuntu SMP Thu Mar 27 18:19:34 UTC 2025 aarch64 aarch64 aarch64 GNU/Linux

### OS 버전 ###
명령어: lsb_release -a 2>/dev/null || cat /etc/os-release
---
Distributor ID:	Ubuntu
Description:	Ubuntu 24.04.1 LTS
Release:	24.04
Codename:	noble

### CPU 정보 ###
명령어: lscpu
---
Architecture:                         aarch64
CPU op-mode(s):                       32-bit, 64-bit
Byte Order:                           Little Endian
CPU(s):                               4
On-line CPU(s) list:                  0-3
Vendor ID:                            ARM
Model name:                           Neoverse-N1
Model:                                1
Thread(s) per core:                   1
Core(s) per socket:                   4
Socket(s):                            1
Stepping:                             r3p1
BogoMIPS:                             50.00
Flags:                                fp asimd evtstrm aes pmull sha1 sha2 crc32 atomics fphp asimdhp cpuid asimdrdm lrcpc dcpop asimddp ssbs
NUMA node(s):                         1
NUMA node0 CPU(s):                    0-3
Vulnerability Gather data sampling:   Not affected
Vulnerability Itlb multihit:          Not affected
Vulnerability L1tf:                   Not affected
Vulnerability Mds:                    Not affected
Vulnerability Meltdown:               Not affected
Vulnerability Mmio stale data:        Not affected
Vulnerability Reg file data sampling: Not affected
Vulnerability Retbleed:               Not affected
Vulnerability Spec rstack overflow:   Not affected
Vulnerability Spec store bypass:      Mitigation; Speculative Store Bypass disabled via prctl
Vulnerability Spectre v1:             Mitigation; __user pointer sanitization
Vulnerability Spectre v2:             Mitigation; CSV2, BHB
Vulnerability Srbds:                  Not affected
Vulnerability Tsx async abort:        Not affected

### 메모리 정보 ###
명령어: free -h
---
               total        used        free      shared  buff/cache   available
Mem:            23Gi       2.9Gi       6.8Gi       268Mi        14Gi        20Gi
Swap:             0B          0B          0B

### 디스크 사용량 ###
명령어: df -h
---
Filesystem          Size  Used Avail Use% Mounted on
tmpfs               2.4G  2.6M  2.4G   1% /run
efivarfs            256K   21K  236K   8% /sys/firmware/efi/efivars
/dev/sda1           193G   63G  130G  33% /
tmpfs                12G  100K   12G   1% /dev/shm
tmpfs               5.0M     0  5.0M   0% /run/lock
/dev/sda16          891M  107M  722M  13% /boot
/dev/sda15           98M  6.4M   92M   7% /boot/efi
onedrive:workspace  1.1T  201G  854G  20% /home/<USER>/workspace
tmpfs               2.4G   48K  2.4G   1% /run/user/1001

### 블록 디바이스 정보 ###
명령어: lsblk
---
NAME    MAJ:MIN RM   SIZE RO TYPE MOUNTPOINTS
loop0     7:0    0  59.6M  1 loop /snap/core20/2573
loop1     7:1    0 178.6M  1 loop /snap/chromium/3124
loop2     7:2    0     4K  1 loop /snap/bare/5
loop3     7:3    0  13.8M  1 loop /snap/astral-uv/564
loop4     7:4    0 178.6M  1 loop /snap/chromium/3136
loop5     7:5    0  48.8M  1 loop /snap/core18/2848
loop6     7:6    0  48.8M  1 loop /snap/core18/2857
loop7     7:7    0  13.8M  1 loop /snap/astral-uv/575
loop9     7:9    0  68.9M  1 loop /snap/core22/1912
loop10    7:10   0  68.9M  1 loop /snap/core22/1966
loop11    7:11   0  59.6M  1 loop /snap/core20/2585
loop12    7:12   0  61.8M  1 loop /snap/core24/892
loop13    7:13   0  66.6M  1 loop /snap/cups/1086
loop14    7:14   0  66.6M  1 loop /snap/cups/1102
loop15    7:15   0 493.5M  1 loop /snap/gnome-42-2204/201
loop16    7:16   0  91.7M  1 loop /snap/gtk-common-themes/1535
loop17    7:17   0  26.2M  1 loop /snap/oracle-cloud-agent/73
loop18    7:18   0    34M  1 loop /snap/oracle-cloud-agent/95
loop19    7:19   0  38.7M  1 loop /snap/snapd/23772
loop20    7:20   0  44.3M  1 loop /snap/snapd/24509
loop21    7:21   0  61.8M  1 loop /snap/core24/992
sda       8:0    0   200G  0 disk 
├─sda1    8:1    0   199G  0 part /
├─sda15   8:15   0    99M  0 part /boot/efi
└─sda16 259:0    0   923M  0 part /boot

[0;34m===== 네트워크 설정 =====[0m

### 네트워크 인터페이스 ###
명령어: ip a
---
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host noprefixroute 
       valid_lft forever preferred_lft forever
2: enp0s6: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 9000 qdisc mq state UP group default qlen 1000
    link/ether 02:00:17:03:36:f1 brd ff:ff:ff:ff:ff:ff
    inet **********/24 metric 100 brd ********** scope global noprefixroute enp0s6
       valid_lft forever preferred_lft forever
    inet6 fe80::17ff:fe03:36f1/64 scope link 
       valid_lft forever preferred_lft forever
3: br-bd6a174e5c8c: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue state UP group default 
    link/ether 02:42:22:55:99:a7 brd ff:ff:ff:ff:ff:ff
    inet **********/16 brd ************** scope global br-bd6a174e5c8c
       valid_lft forever preferred_lft forever
    inet6 fe80::42:22ff:fe55:99a7/64 scope link 
       valid_lft forever preferred_lft forever
4: docker0: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc noqueue state DOWN group default 
    link/ether 02:42:e1:0b:b4:c5 brd ff:ff:ff:ff:ff:ff
    inet **********/16 brd 172.17.255.255 scope global docker0
       valid_lft forever preferred_lft forever
6: veth632c539@if5: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 52:db:83:a4:48:8d brd ff:ff:ff:ff:ff:ff link-netnsid 0
    inet6 fe80::50db:83ff:fea4:488d/64 scope link 
       valid_lft forever preferred_lft forever
8: vethba8baf4@if7: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 2a:e8:20:4a:e1:59 brd ff:ff:ff:ff:ff:ff link-netnsid 1
    inet6 fe80::28e8:20ff:fe4a:e159/64 scope link 
       valid_lft forever preferred_lft forever
10: veth0e52850@if9: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 26:76:2f:17:aa:2a brd ff:ff:ff:ff:ff:ff link-netnsid 2
    inet6 fe80::2476:2fff:fe17:aa2a/64 scope link 
       valid_lft forever preferred_lft forever
12: veth61b82e5@if11: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 42:c7:72:de:81:d4 brd ff:ff:ff:ff:ff:ff link-netnsid 3
    inet6 fe80::40c7:72ff:fede:81d4/64 scope link 
       valid_lft forever preferred_lft forever
14: veth86ce88e@if13: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether c2:59:54:0f:15:e8 brd ff:ff:ff:ff:ff:ff link-netnsid 5
    inet6 fe80::c059:54ff:fe0f:15e8/64 scope link 
       valid_lft forever preferred_lft forever
16: vethc16a4a9@if15: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 42:30:f1:2c:fe:58 brd ff:ff:ff:ff:ff:ff link-netnsid 4
    inet6 fe80::4030:f1ff:fe2c:fe58/64 scope link 
       valid_lft forever preferred_lft forever
20: vethb4f0013@if19: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 2a:5b:f4:cb:0d:7d brd ff:ff:ff:ff:ff:ff link-netnsid 7
    inet6 fe80::285b:f4ff:fecb:d7d/64 scope link 
       valid_lft forever preferred_lft forever
22: veth9185b99@if21: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 12:dc:7c:1c:8e:51 brd ff:ff:ff:ff:ff:ff link-netnsid 8
    inet6 fe80::10dc:7cff:fe1c:8e51/64 scope link 
       valid_lft forever preferred_lft forever
24: veth1a8581b@if23: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether fa:ba:bf:df:39:31 brd ff:ff:ff:ff:ff:ff link-netnsid 9
    inet6 fe80::f8ba:bfff:fedf:3931/64 scope link 
       valid_lft forever preferred_lft forever
26: vethac0696a@if25: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 2e:e5:da:ac:34:39 brd ff:ff:ff:ff:ff:ff link-netnsid 10
    inet6 fe80::2ce5:daff:feac:3439/64 scope link 
       valid_lft forever preferred_lft forever
38: veth6d82b07@if37: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master br-bd6a174e5c8c state UP group default 
    link/ether 46:2a:a8:13:34:74 brd ff:ff:ff:ff:ff:ff link-netnsid 6
    inet6 fe80::442a:a8ff:fe13:3474/64 scope link 
       valid_lft forever preferred_lft forever

### 라우팅 테이블 ###
명령어: ip route
---
default via ******** dev enp0s6 proto dhcp src ********** metric 100 
default via ******** dev enp0s6 proto dhcp src ********** metric 1002 mtu 9000 
10.0.0.0/24 dev enp0s6 proto dhcp scope link src ********** metric 1002 mtu 9000 
******** dev enp0s6 proto dhcp scope link src ********** metric 100 
***********/16 dev enp0s6 proto dhcp scope link src ********** metric 100 
***********/16 dev enp0s6 proto dhcp scope link src ********** metric 1002 mtu 9000 
*************** dev enp0s6 proto dhcp scope link src ********** metric 100 
**********/16 dev docker0 proto kernel scope link src ********** linkdown 
**********/16 dev br-bd6a174e5c8c proto kernel scope link src ********** 

### DNS 설정 ###
명령어: cat /etc/resolv.conf
---
# This is /run/systemd/resolve/stub-resolv.conf managed by man:systemd-resolved(8).
# Do not edit.
#
# This file might be symlinked as /etc/resolv.conf. If you're looking at
# /etc/resolv.conf and seeing this text, you have followed the symlink.
#
# This is a dynamic resolv.conf file for connecting local clients to the
# internal DNS stub resolver of systemd-resolved. This file lists all
# configured search domains.
#
# Run "resolvectl status" to see details about the uplink DNS servers
# currently in use.
#
# Third party programs should typically not access this file directly, but only
# through the symlink at /etc/resolv.conf. To manage man:resolv.conf(5) in a
# different way, replace this symlink by a static file or a different symlink.
#
# See man:systemd-resolved.service(8) for details about the supported modes of
# operation for /etc/resolv.conf.

nameserver **********
options edns0 trust-ad
search vcn02030804.oraclevcn.com

### 네트워크 연결 상태 ###
명령어: ss -tuln | head -20
---
Netid State  Recv-Q Send-Q     Local Address:Port  Peer Address:PortProcess
udp   UNCONN 0      0             **********:53         0.0.0.0:*          
udp   UNCONN 0      0          **********%lo:53         0.0.0.0:*          
udp   UNCONN 0      0      **********%enp0s6:68         0.0.0.0:*          
udp   UNCONN 0      0                0.0.0.0:111        0.0.0.0:*          
udp   UNCONN 0      0                0.0.0.0:35268      0.0.0.0:*          
udp   UNCONN 0      0                0.0.0.0:5353       0.0.0.0:*          
udp   UNCONN 0      0                   [::]:111           [::]:*          
udp   UNCONN 0      0                   [::]:53718         [::]:*          
udp   UNCONN 0      0                   [::]:5353          [::]:*          
tcp   LISTEN 0      4096          **********:53         0.0.0.0:*          
tcp   LISTEN 0      4096           127.0.0.1:27017      0.0.0.0:*          
tcp   LISTEN 0      4096             0.0.0.0:54324      0.0.0.0:*          
tcp   LISTEN 0      4096             0.0.0.0:54327      0.0.0.0:*          
tcp   LISTEN 0      4096             0.0.0.0:54321      0.0.0.0:*          
tcp   LISTEN 0      4096             0.0.0.0:54322      0.0.0.0:*          
tcp   LISTEN 0      4096             0.0.0.0:54323      0.0.0.0:*          
tcp   LISTEN 0      511              0.0.0.0:3090       0.0.0.0:*          
tcp   LISTEN 0      4096             0.0.0.0:111        0.0.0.0:*          
tcp   LISTEN 0      5                0.0.0.0:873        0.0.0.0:*          

[0;34m===== 마운트 및 파일시스템 정보 =====[0m

### 전체 마운트 정보 ###
명령어: mount -v
---
sysfs on /sys type sysfs (rw,nosuid,nodev,noexec,relatime)
proc on /proc type proc (rw,nosuid,nodev,noexec,relatime)
udev on /dev type devtmpfs (rw,nosuid,relatime,size=12242384k,nr_inodes=3060596,mode=755,inode64)
devpts on /dev/pts type devpts (rw,nosuid,noexec,relatime,gid=5,mode=620,ptmxmode=000)
tmpfs on /run type tmpfs (rw,nosuid,nodev,noexec,relatime,size=2455600k,mode=755,inode64)
efivarfs on /sys/firmware/efi/efivars type efivarfs (rw,nosuid,nodev,noexec,relatime)
/dev/sda1 on / type ext4 (rw,relatime,discard,errors=remount-ro,commit=30)
securityfs on /sys/kernel/security type securityfs (rw,nosuid,nodev,noexec,relatime)
tmpfs on /dev/shm type tmpfs (rw,nosuid,nodev,inode64)
tmpfs on /run/lock type tmpfs (rw,nosuid,nodev,noexec,relatime,size=5120k,inode64)
cgroup2 on /sys/fs/cgroup type cgroup2 (rw,nosuid,nodev,noexec,relatime,nsdelegate,memory_recursiveprot)
pstore on /sys/fs/pstore type pstore (rw,nosuid,nodev,noexec,relatime)
bpf on /sys/fs/bpf type bpf (rw,nosuid,nodev,noexec,relatime,mode=700)
systemd-1 on /proc/sys/fs/binfmt_misc type autofs (rw,relatime,fd=32,pgrp=1,timeout=0,minproto=5,maxproto=5,direct,pipe_ino=4258)
hugetlbfs on /dev/hugepages type hugetlbfs (rw,nosuid,nodev,relatime,pagesize=2M)
mqueue on /dev/mqueue type mqueue (rw,nosuid,nodev,noexec,relatime)
debugfs on /sys/kernel/debug type debugfs (rw,nosuid,nodev,noexec,relatime)
tracefs on /sys/kernel/tracing type tracefs (rw,nosuid,nodev,noexec,relatime)
fusectl on /sys/fs/fuse/connections type fusectl (rw,nosuid,nodev,noexec,relatime)
configfs on /sys/kernel/config type configfs (rw,nosuid,nodev,noexec,relatime)
/var/lib/snapd/snaps/bare_5.snap on /snap/bare/5 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/core18_2848.snap on /snap/core18/2848 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/core18_2857.snap on /snap/core18/2857 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/core22_1912.snap on /snap/core22/1912 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/core22_1966.snap on /snap/core22/1966 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/core24_892.snap on /snap/core24/892 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/cups_1086.snap on /snap/cups/1086 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/cups_1102.snap on /snap/cups/1102 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/gnome-42-2204_201.snap on /snap/gnome-42-2204/201 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/gtk-common-themes_1535.snap on /snap/gtk-common-themes/1535 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/oracle-cloud-agent_73.snap on /snap/oracle-cloud-agent/73 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/oracle-cloud-agent_95.snap on /snap/oracle-cloud-agent/95 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/snapd_23772.snap on /snap/snapd/23772 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/snapd_24509.snap on /snap/snapd/24509 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/dev/sda16 on /boot type ext4 (rw,relatime)
/dev/sda15 on /boot/efi type vfat (rw,relatime,fmask=0077,dmask=0077,codepage=437,iocharset=iso8859-1,shortname=mixed,errors=remount-ro)
binfmt_misc on /proc/sys/fs/binfmt_misc type binfmt_misc (rw,nosuid,nodev,noexec,relatime)
sunrpc on /run/rpc_pipefs type rpc_pipefs (rw,relatime)
tmpfs on /run/snapd/ns type tmpfs (rw,nosuid,nodev,noexec,relatime,size=2455600k,mode=755,inode64)
nsfs on /run/snapd/ns/cups.mnt type nsfs (rw)
onedrive:workspace on /home/<USER>/workspace type fuse.rclone (rw,nosuid,nodev,relatime,user_id=1001,group_id=1001)
overlay on /var/lib/docker/overlay2/e17b875fc839053a89ad6e671abb3402168174201f149a82e9239879548e9826/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/3QWGV57AUPOJ6F3WZN3X7SOXAW:/var/lib/docker/overlay2/l/V64XEKEJZNEGLMS63MC27E5QSF:/var/lib/docker/overlay2/l/ZX6CI3RUUUBDXS2GL3SMPRWQS7:/var/lib/docker/overlay2/l/AVL6M2IDTGLRBDQELXRLUBVCOS:/var/lib/docker/overlay2/l/AKIP5GTR7GMNH5KUP5OJM7545T:/var/lib/docker/overlay2/l/GV7F2FTLUF5UOJXHYOTJSI47M5:/var/lib/docker/overlay2/l/PLCB3MWAIH5D5FPNM27NUEFC42:/var/lib/docker/overlay2/l/CH4CEA7UAEVOAU55S6J3TTQ7DO:/var/lib/docker/overlay2/l/IFSOPPDPJOXZ23MD3ITTNOMPRB,upperdir=/var/lib/docker/overlay2/e17b875fc839053a89ad6e671abb3402168174201f149a82e9239879548e9826/diff,workdir=/var/lib/docker/overlay2/e17b875fc839053a89ad6e671abb3402168174201f149a82e9239879548e9826/work,nouserxattr)
overlay on /var/lib/docker/overlay2/7ec5c1571be737403622444b538d83a0f3127ca0e78584c848a343f8d4dccd5f/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/EYVSYXPEK3TTXJYMJVTTLDZ4EM:/var/lib/docker/overlay2/l/VURKIQ3BXSLMZ5MOYVLCDOIBNX:/var/lib/docker/overlay2/l/SQPU64IPOIH4SEHSBDQEK7KR2V:/var/lib/docker/overlay2/l/LRHUYZPSLFKY5V3M3GJ24KYYUF:/var/lib/docker/overlay2/l/GSRFAW7YV6LLUHSVL5VHS7Z74O:/var/lib/docker/overlay2/l/YO5OMOQDPOT4WMHONPS6U26NZJ:/var/lib/docker/overlay2/l/XZWIZ2IQ2F6I4PE4DRRV6DMKMH:/var/lib/docker/overlay2/l/EU7IDP4YFDTMB7D2M2GY3LLFVC:/var/lib/docker/overlay2/l/HLEGHHC2JCTHAX33VN3H3OKC5D:/var/lib/docker/overlay2/l/LTX7E6DGACOFMCNBUF73XXTWLR,upperdir=/var/lib/docker/overlay2/7ec5c1571be737403622444b538d83a0f3127ca0e78584c848a343f8d4dccd5f/diff,workdir=/var/lib/docker/overlay2/7ec5c1571be737403622444b538d83a0f3127ca0e78584c848a343f8d4dccd5f/work,nouserxattr)
overlay on /var/lib/docker/overlay2/43adc924ab680c899856ed4a1d8af1f95d7a6354c78cdf7714212b2e28b59ab0/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/QZTX766UWA472OJ4K3AVXWQJ74:/var/lib/docker/overlay2/l/V5HOWB4FC7U7X2M3U44HNYAOAY:/var/lib/docker/overlay2/l/CN37K2FE7Y7ODKACTCH3VGQIWH:/var/lib/docker/overlay2/l/J7KAWF37LQCO3KOQSEUCKJQTP6:/var/lib/docker/overlay2/l/4J7KV2ZC3STSNCPGDNGMXW3Q46,upperdir=/var/lib/docker/overlay2/43adc924ab680c899856ed4a1d8af1f95d7a6354c78cdf7714212b2e28b59ab0/diff,workdir=/var/lib/docker/overlay2/43adc924ab680c899856ed4a1d8af1f95d7a6354c78cdf7714212b2e28b59ab0/work,nouserxattr)
overlay on /var/lib/docker/overlay2/0ffcf2b1528fff4e24a196ebb6a406c8abb3b6b7ae3f02bb5e3c3ab843175c5d/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/DAFPZQRID2ONNB3CUTEWS2XJP6:/var/lib/docker/overlay2/l/56DLBERTC5DYIWL6VLCPRSVWRF:/var/lib/docker/overlay2/l/Z3TRJKMHNQK3GQSY6I4WCPUGXN:/var/lib/docker/overlay2/l/BEI6AJTLSLN7FAP5KRIXQBT4UZ,upperdir=/var/lib/docker/overlay2/0ffcf2b1528fff4e24a196ebb6a406c8abb3b6b7ae3f02bb5e3c3ab843175c5d/diff,workdir=/var/lib/docker/overlay2/0ffcf2b1528fff4e24a196ebb6a406c8abb3b6b7ae3f02bb5e3c3ab843175c5d/work,nouserxattr)
overlay on /var/lib/docker/overlay2/fa7c4b53b3da5313b4594c977fd5855831f909f40ce83f48ee24ad01007c3c81/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/FOK43JUX6NY6QTV7B2XCFX5DOO:/var/lib/docker/overlay2/l/CRN3SPOWXVCH2UYMR6UK5X55KH:/var/lib/docker/overlay2/l/3XLYKBMSYZTYG44CXPNALL62HG:/var/lib/docker/overlay2/l/YDLF6RUXYLYGTCCTK4UKIUA52C:/var/lib/docker/overlay2/l/PJUV3QY3Z42C5P73WEPLVSIQT7:/var/lib/docker/overlay2/l/JJP67LCMHFECQX3S3V7CV32IZ2:/var/lib/docker/overlay2/l/OTGIH6ETIP5TK5KKPTF523DX45:/var/lib/docker/overlay2/l/A6BH5JTRIFKJTKSDBT4WTF2A7V:/var/lib/docker/overlay2/l/ML63ENNA2ZVCOHTJYE3G4ZUYZW,upperdir=/var/lib/docker/overlay2/fa7c4b53b3da5313b4594c977fd5855831f909f40ce83f48ee24ad01007c3c81/diff,workdir=/var/lib/docker/overlay2/fa7c4b53b3da5313b4594c977fd5855831f909f40ce83f48ee24ad01007c3c81/work,nouserxattr)
overlay on /var/lib/docker/overlay2/27ed675726ff7a1905e2acff87ebf5a64896b74dc396b73161475d6afcf388d9/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/DTVMJ4J5PCU3XUUC4KHV6XD64C:/var/lib/docker/overlay2/l/25YL4PRCCZKNZEP5IARJKYPQML:/var/lib/docker/overlay2/l/ZWBFGTRMNPHXRO7LR4ULGKK4LU:/var/lib/docker/overlay2/l/PXIO5AZHFBFGB2IA7GJUJ4GC7A:/var/lib/docker/overlay2/l/EW6MDF7PHQHX7UDBBK7AGTN6D6:/var/lib/docker/overlay2/l/CA2GZCKMNC5MBCGNVLLP73E2GB:/var/lib/docker/overlay2/l/ZPFTU2WWS7NZD3FGYYJNCXTVRI:/var/lib/docker/overlay2/l/W7PXYKJ3QHJOLP7D475M6LJZWR:/var/lib/docker/overlay2/l/HSX3KV2K626OGWP5DZWEP4HYPG:/var/lib/docker/overlay2/l/EZ2Z2SIRQPXJZCRBH3BR43KDOT:/var/lib/docker/overlay2/l/NSKNXY3ZNRIJWSJ3M6JLFSRRMU:/var/lib/docker/overlay2/l/6Q54YEKOVRLWMJWM67HN4UCB2V,upperdir=/var/lib/docker/overlay2/27ed675726ff7a1905e2acff87ebf5a64896b74dc396b73161475d6afcf388d9/diff,workdir=/var/lib/docker/overlay2/27ed675726ff7a1905e2acff87ebf5a64896b74dc396b73161475d6afcf388d9/work,nouserxattr)
overlay on /var/lib/docker/overlay2/95d9088b8990e8dd63cbef2b1432a2fd0a86f999269823709b727e024c372c87/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/4GN5JEZUIKVIK3UV3H66MHXN5I:/var/lib/docker/overlay2/l/YKTL6EXPX7GYSTDJ2YSKQXWB5J:/var/lib/docker/overlay2/l/N2QSXY3NXF5MQEFGHIEXLA3YW3:/var/lib/docker/overlay2/l/XZQJ2XSA2CRJ7QX7Z32F6SJW75:/var/lib/docker/overlay2/l/PA2I5MWD2W3F6JCJRLMUGBQZN7:/var/lib/docker/overlay2/l/G6NPBKGYDEC3KA34V773FW55DY:/var/lib/docker/overlay2/l/XTE23LUJZWHPNQZS2RZD6SZA3Z,upperdir=/var/lib/docker/overlay2/95d9088b8990e8dd63cbef2b1432a2fd0a86f999269823709b727e024c372c87/diff,workdir=/var/lib/docker/overlay2/95d9088b8990e8dd63cbef2b1432a2fd0a86f999269823709b727e024c372c87/work,nouserxattr)
overlay on /var/lib/docker/overlay2/9b6c5a3c067dbc2c5984e557bfab12c8b36e4a545a1933e87c669cfb32af681b/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/Q3OED6D7UBJOOUDLSBBS7BU2BV:/var/lib/docker/overlay2/l/H7DLV2BHVC34ZQUCGTTMDGNBXL:/var/lib/docker/overlay2/l/IPFDLVFMZEOVAQKUYB3F3TJ3G4:/var/lib/docker/overlay2/l/7OP3C6GXJDA6KNWYROQVWDT5KV:/var/lib/docker/overlay2/l/GF7CHUA2BJ6YBXSPM4WWAJFLQL:/var/lib/docker/overlay2/l/VFZHGZK664546IFCVQMDNKSIFG:/var/lib/docker/overlay2/l/N5EHAYPYU4TWEDWGLIJQQARBSB:/var/lib/docker/overlay2/l/Z2BQUO7TEBYVXAT2N56YKMTOER:/var/lib/docker/overlay2/l/DTQMFBGNVOKC5JPQJ4JDYLS2SF:/var/lib/docker/overlay2/l/7MMUCFROADBIVVQ3NDAASL4BW2:/var/lib/docker/overlay2/l/X4HNH4DZUSUR7JNTVPTHSWPNQC,upperdir=/var/lib/docker/overlay2/9b6c5a3c067dbc2c5984e557bfab12c8b36e4a545a1933e87c669cfb32af681b/diff,workdir=/var/lib/docker/overlay2/9b6c5a3c067dbc2c5984e557bfab12c8b36e4a545a1933e87c669cfb32af681b/work,nouserxattr)
overlay on /var/lib/docker/overlay2/bbb8daeaf00f1c02b3352e3b9ac34c68819111e55fa41c59bda035f251f7dd8e/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/HJGLAPV6WNJR5UQXL25B6OBTKT:/var/lib/docker/overlay2/l/CB6MMMRXPKKX2UKOYMXUYZXGDK:/var/lib/docker/overlay2/l/KS4VDRHQRNS4TXZSYF6S6LHK72:/var/lib/docker/overlay2/l/DWBHUQP75OXIIX44GJQKNIOEIO:/var/lib/docker/overlay2/l/ICT5J7UD4VG42VDGIKZSOSEOO7:/var/lib/docker/overlay2/l/QNC74CSJ2UC7IKZOPG3SWXWI4B:/var/lib/docker/overlay2/l/HRA3BDFHQ6FONIPVAGOXW37YZV:/var/lib/docker/overlay2/l/P4K7VME5D2U5OGIKGNNSPINWCU:/var/lib/docker/overlay2/l/4DNV2HROT2WQSJ6FZTP73OMI32:/var/lib/docker/overlay2/l/67HGRQRBFIZJXBWEQTXHOCHHEB:/var/lib/docker/overlay2/l/LB5BMJYYTLJLHVZJIHZES6ROV3,upperdir=/var/lib/docker/overlay2/bbb8daeaf00f1c02b3352e3b9ac34c68819111e55fa41c59bda035f251f7dd8e/diff,workdir=/var/lib/docker/overlay2/bbb8daeaf00f1c02b3352e3b9ac34c68819111e55fa41c59bda035f251f7dd8e/work,nouserxattr)
overlay on /var/lib/docker/overlay2/c78491bbca09dd2648f729cc3c4927db17448ebd8e0bba7a881a28db058de66a/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/6FLTU2FQ7A3WWRHXOSDA7QXJNJ:/var/lib/docker/overlay2/l/SUM62WBNETHDG5PBSITIXIZSIX:/var/lib/docker/overlay2/l/7AYGRYVAUTTEX24TG6PWBQPP7O:/var/lib/docker/overlay2/l/RIHRE6KJS5O5LYSYPH2HTYK2GH:/var/lib/docker/overlay2/l/RPRVUZOHWKYMQEUZJ2HMR7APWZ:/var/lib/docker/overlay2/l/FDWCFCV4KWHUGME2P5FTWBV5GE:/var/lib/docker/overlay2/l/UKT22742C5YDPK3SQELPQUSMPR:/var/lib/docker/overlay2/l/DVJAHY655B44FTYQGB7UTQVPLJ:/var/lib/docker/overlay2/l/L6OM4VF7T7N3SIRGOAR5GJYXHC:/var/lib/docker/overlay2/l/MAWTPSVEA6JKGSGF3USXPO2IUX:/var/lib/docker/overlay2/l/N3Z4OQTEQVLUSBTTO66JCWTWC6:/var/lib/docker/overlay2/l/UVKWOA5EOR7VSRBMLMQSHAUDK4:/var/lib/docker/overlay2/l/G7TMWIAL7VTWWKFOAPZ3XEDYBZ:/var/lib/docker/overlay2/l/BE5Q7SUTFOFAPRLV65DYRNO3JQ:/var/lib/docker/overlay2/l/UWYAFVFVXXKW7FO34WLXYK7KTO:/var/lib/docker/overlay2/l/DNHFL7WWNE7V6KORSGUCR2RFU6:/var/lib/docker/overlay2/l/U6INYZZDQS4Z3YZAPYG4J22XNC:/var/lib/docker/overlay2/l/23LLO2ERSW5D5BU7OKSIJCR4QF:/var/lib/docker/overlay2/l/2CEFMNCSMDPRU3MJHDTPEMWJXB:/var/lib/docker/overlay2/l/ULWJYV6NQUTSTEFDXX7ZLSB4PV:/var/lib/docker/overlay2/l/SKICSA7IKEQKETKI2PYVCLASX7:/var/lib/docker/overlay2/l/JBV2Q7FM3BCBNJ7KAA372JQR5N:/var/lib/docker/overlay2/l/ZIRL3QWGRGLSETEJ7UFCZX255Y:/var/lib/docker/overlay2/l/DGGHPWBOAULEEQGDZFNSTV6QNZ:/var/lib/docker/overlay2/l/RLFOSENFSOCSNPJ2BNZDJAP7HR:/var/lib/docker/overlay2/l/VAG2L43U3HNT4572MVMYD2QBIG:/var/lib/docker/overlay2/l/IWQ5OCNJKM5DXEFTYRRXRGL2WA:/var/lib/docker/overlay2/l/KE7RGER4I5JFFEQCIUG24LPEE3:/var/lib/docker/overlay2/l/5VW4D22QGR6NJ6WKHT6BZ2DODV:/var/lib/docker/overlay2/l/7DHXGJ3O34BJHV66QS4R2YMICI:/var/lib/docker/overlay2/l/A433LB3HXTXQGS6TYSDLMS7S5Q:/var/lib/docker/overlay2/l/ERJVS5U4Z6PPS35YFLUKC7LJF6:/var/lib/docker/overlay2/l/4TCC73W76VYUJKDZVO6RPCUIU6:/var/lib/docker/overlay2/l/5UYSGCO2ZCE3WTWBQ3OPL5FZ2G:/var/lib/docker/overlay2/l/WRRNAAYJG7JSCLY6CO7KUMQ2UI:/var/lib/docker/overlay2/l/BI7XVBV5ZAZKXJMRVEBUQ3IDSS:/var/lib/docker/overlay2/l/KVJ6IT3MOIENNDNQDGB3DSZ7E5:/var/lib/docker/overlay2/l/CWB27IPJS7BDUVMSG6IPJ7LOR2:/var/lib/docker/overlay2/l/7KZPKIE2JN4PG4QZCCN6PMOV5A:/var/lib/docker/overlay2/l/GIEEXQRRDQHOOJ2VSW7B7AF5GP:/var/lib/docker/overlay2/l/X3OZMKFPLD2EEOQ336E3QQ2YXG:/var/lib/docker/overlay2/l/WDDARJV3I5Q2V4GACT2FSPQMDH:/var/lib/docker/overlay2/l/4Z6P4ETRNUFTYVDTQKMQHT3PMP:/var/lib/docker/overlay2/l/6G2H3LFWT2VQR7O4F6NAFT6KGB:/var/lib/docker/overlay2/l/KJTXGWAJ7HD62324U25LRAVLHU:/var/lib/docker/overlay2/l/Q65C3SVVAQJZ4WURRMGU4LHIEA:/var/lib/docker/overlay2/l/JHLF5JDVRFTEFMFJJDEQ5YZYH2,upperdir=/var/lib/docker/overlay2/c78491bbca09dd2648f729cc3c4927db17448ebd8e0bba7a881a28db058de66a/diff,workdir=/var/lib/docker/overlay2/c78491bbca09dd2648f729cc3c4927db17448ebd8e0bba7a881a28db058de66a/work,nouserxattr)
nsfs on /run/docker/netns/e2b926555e87 type nsfs (rw)
nsfs on /run/docker/netns/7b1745507340 type nsfs (rw)
nsfs on /run/docker/netns/e53bd6d8a765 type nsfs (rw)
nsfs on /run/docker/netns/c994c29e5c11 type nsfs (rw)
nsfs on /run/docker/netns/1c9eba6fafae type nsfs (rw)
nsfs on /run/docker/netns/8e186037d369 type nsfs (rw)
nsfs on /run/docker/netns/cbcb72b6b044 type nsfs (rw)
nsfs on /run/docker/netns/86b7f1484ce6 type nsfs (rw)
nsfs on /run/docker/netns/59ba596ea61b type nsfs (rw)
nsfs on /run/docker/netns/5dcd9c64db12 type nsfs (rw)
overlay on /var/lib/docker/overlay2/755fc6e687d0794659ec603c835abea5ba84b86b7483a149017a4803a247c994/merged type overlay (rw,relatime,lowerdir=/var/lib/docker/overlay2/l/BAHGWYACBWPGPJ6HRC6GQR26CP:/var/lib/docker/overlay2/l/6MOAJ43E5HM4VNZL3HVPNOXR27:/var/lib/docker/overlay2/l/WY57X3277IPKRDR57NEKWCYVKT:/var/lib/docker/overlay2/l/WRAXUZUX7NINYPFOX3J6S77ZC7:/var/lib/docker/overlay2/l/SDM4HFTIU5RLL26GB7JANM7KOR:/var/lib/docker/overlay2/l/DXAZOBHZ6QF5KEPCYRFVSPW3CR:/var/lib/docker/overlay2/l/X4HNH4DZUSUR7JNTVPTHSWPNQC,upperdir=/var/lib/docker/overlay2/755fc6e687d0794659ec603c835abea5ba84b86b7483a149017a4803a247c994/diff,workdir=/var/lib/docker/overlay2/755fc6e687d0794659ec603c835abea5ba84b86b7483a149017a4803a247c994/work,nouserxattr)
nsfs on /run/docker/netns/ccb34e0a397b type nsfs (rw)
nsfs on /run/snapd/ns/chromium.mnt type nsfs (rw)
/var/lib/snapd/snaps/astral-uv_564.snap on /snap/astral-uv/564 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/core20_2573.snap on /snap/core20/2573 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/astral-uv_575.snap on /snap/astral-uv/575 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
tmpfs on /run/user/1001 type tmpfs (rw,nosuid,nodev,relatime,size=2455600k,nr_inodes=613900,mode=700,uid=1001,gid=1001,inode64)
/var/lib/snapd/snaps/chromium_3124.snap on /snap/chromium/3124 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/chromium_3136.snap on /snap/chromium/3136 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/core24_992.snap on /snap/core24/992 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)
/var/lib/snapd/snaps/core20_2585.snap on /snap/core20/2585 type squashfs (ro,nodev,relatime,errors=continue,threads=single,x-gdu.hide,x-gvfs-hide)

### 파일시스템 타입별 마운트 ###
명령어: findmnt -t fuse,fuse.rclone,cifs,nfs
---
TARGET                 SOURCE             FSTYPE      OPTIONS
/home/<USER>/workspace onedrive:workspace fuse.rclone rw,nosuid,nodev,relatime,user_id=1001,group_id=1001

### OneDrive 관련 마운트 검색 ###
명령어: mount | grep -i 'onedrive\|rclone\|fuse'
---
fusectl on /sys/fs/fuse/connections type fusectl (rw,nosuid,nodev,noexec,relatime)
onedrive:workspace on /home/<USER>/workspace type fuse.rclone (rw,nosuid,nodev,relatime,user_id=1001,group_id=1001)

### fstab 내용 ###
명령어: cat /etc/fstab
---
LABEL=cloudimg-rootfs	/	 ext4	discard,commit=30,errors=remount-ro	0 1
LABEL=BOOT	/boot	ext4	defaults	0 2
LABEL=UEFI	/boot/efi	vfat	umask=0077	0 1

# CLOUD_IMG: This file was created/modified by the Cloud Image build process
######################################
## ORACLE CLOUD INFRASTRUCTURE CUSTOMERS
##
## If you are adding an iSCSI remote block volume to this file you MUST
## include the '_netdev' mount option or your instance will become
## unavailable after the next reboot.
## SCSI device names are not stable across reboots; please use the device UUID
## instead of /dev path.
##
## Example:
## UUID="94c5aade-8bb1-4d55-ad0c-388bb8aa716a" /data1 ext4 defaults,noatime,_netdev 0 2
##
## More information:
## https://docs.us-phoenix-1.oraclecloud.com/Content/Block/Tasks/connectingtoavolume.htm
##

### systemd 마운트 유닛 ###
명령어: systemctl list-units --type=mount
---
  UNIT                                                                                                  LOAD   ACTIVE SUB     DESCRIPTION
  -.mount                                                                                               loaded active mounted Root Mount
  boot-efi.mount                                                                                        loaded active mounted /boot/efi
  boot.mount                                                                                            loaded active mounted /boot
  dev-hugepages.mount                                                                                   loaded active mounted Huge Pages File System
  dev-mqueue.mount                                                                                      loaded active mounted POSIX Message Queue File System
  home-ubuntu-workspace.mount                                                                           loaded active mounted /home/<USER>/workspace
  proc-sys-fs-binfmt_misc.mount                                                                         loaded active mounted Arbitrary Executable File Formats File System
  run-docker-netns-1c9eba6fafae.mount                                                                   loaded active mounted /run/docker/netns/1c9eba6fafae
  run-docker-netns-59ba596ea61b.mount                                                                   loaded active mounted /run/docker/netns/59ba596ea61b
  run-docker-netns-5dcd9c64db12.mount                                                                   loaded active mounted /run/docker/netns/5dcd9c64db12
  run-docker-netns-7b1745507340.mount                                                                   loaded active mounted /run/docker/netns/7b1745507340
  run-docker-netns-86b7f1484ce6.mount                                                                   loaded active mounted /run/docker/netns/86b7f1484ce6
  run-docker-netns-8e186037d369.mount                                                                   loaded active mounted /run/docker/netns/8e186037d369
  run-docker-netns-c994c29e5c11.mount                                                                   loaded active mounted /run/docker/netns/c994c29e5c11
  run-docker-netns-cbcb72b6b044.mount                                                                   loaded active mounted /run/docker/netns/cbcb72b6b044
  run-docker-netns-ccb34e0a397b.mount                                                                   loaded active mounted /run/docker/netns/ccb34e0a397b
  run-docker-netns-e2b926555e87.mount                                                                   loaded active mounted /run/docker/netns/e2b926555e87
  run-docker-netns-e53bd6d8a765.mount                                                                   loaded active mounted /run/docker/netns/e53bd6d8a765
  run-rpc_pipefs.mount                                                                                  loaded active mounted RPC Pipe File System
  run-snapd-ns-chromium.mnt.mount                                                                       loaded active mounted /run/snapd/ns/chromium.mnt
  run-snapd-ns-cups.mnt.mount                                                                           loaded active mounted /run/snapd/ns/cups.mnt
  run-snapd-ns.mount                                                                                    loaded active mounted /run/snapd/ns
  run-user-1001.mount                                                                                   loaded active mounted /run/user/1001
  snap-astral\x2duv-564.mount                                                                           loaded active mounted Mount unit for astral-uv, revision 564
  snap-astral\x2duv-575.mount                                                                           loaded active mounted Mount unit for astral-uv, revision 575
  snap-bare-5.mount                                                                                     loaded active mounted Mount unit for bare, revision 5
  snap-chromium-3124.mount                                                                              loaded active mounted Mount unit for chromium, revision 3124
  snap-chromium-3136.mount                                                                              loaded active mounted Mount unit for chromium, revision 3136
  snap-core18-2848.mount                                                                                loaded active mounted Mount unit for core18, revision 2848
  snap-core18-2857.mount                                                                                loaded active mounted Mount unit for core18, revision 2857
  snap-core20-2573.mount                                                                                loaded active mounted Mount unit for core20, revision 2573
  snap-core20-2585.mount                                                                                loaded active mounted Mount unit for core20, revision 2585
  snap-core22-1912.mount                                                                                loaded active mounted Mount unit for core22, revision 1912
  snap-core22-1966.mount                                                                                loaded active mounted Mount unit for core22, revision 1966
  snap-core24-892.mount                                                                                 loaded active mounted Mount unit for core24, revision 892
  snap-core24-992.mount                                                                                 loaded active mounted Mount unit for core24, revision 992
  snap-cups-1086.mount                                                                                  loaded active mounted Mount unit for cups, revision 1086
  snap-cups-1102.mount                                                                                  loaded active mounted Mount unit for cups, revision 1102
  snap-gnome\x2d42\x2d2204-201.mount                                                                    loaded active mounted Mount unit for gnome-42-2204, revision 201
  snap-gtk\x2dcommon\x2dthemes-1535.mount                                                               loaded active mounted Mount unit for gtk-common-themes, revision 1535
  snap-oracle\x2dcloud\x2dagent-73.mount                                                                loaded active mounted Mount unit for oracle-cloud-agent, revision 73
  snap-oracle\x2dcloud\x2dagent-95.mount                                                                loaded active mounted Mount unit for oracle-cloud-agent, revision 95
  snap-snapd-23772.mount                                                                                loaded active mounted Mount unit for snapd, revision 23772
  snap-snapd-24509.mount                                                                                loaded active mounted Mount unit for snapd, revision 24509
  sys-fs-fuse-connections.mount                                                                         loaded active mounted FUSE Control File System
  sys-kernel-config.mount                                                                               loaded active mounted Kernel Configuration File System
  sys-kernel-debug.mount                                                                                loaded active mounted Kernel Debug File System
  sys-kernel-tracing.mount                                                                              loaded active mounted Kernel Trace File System
  var-lib-docker-overlay2-0ffcf2b1528fff4e24a196ebb6a406c8abb3b6b7ae3f02bb5e3c3ab843175c5d-merged.mount loaded active mounted /var/lib/docker/overlay2/0ffcf2b1528fff4e24a196ebb6a406c8abb3b6b7ae3f02bb5e3c3ab843175c5d/merged
  var-lib-docker-overlay2-27ed675726ff7a1905e2acff87ebf5a64896b74dc396b73161475d6afcf388d9-merged.mount loaded active mounted /var/lib/docker/overlay2/27ed675726ff7a1905e2acff87ebf5a64896b74dc396b73161475d6afcf388d9/merged
  var-lib-docker-overlay2-43adc924ab680c899856ed4a1d8af1f95d7a6354c78cdf7714212b2e28b59ab0-merged.mount loaded active mounted /var/lib/docker/overlay2/43adc924ab680c899856ed4a1d8af1f95d7a6354c78cdf7714212b2e28b59ab0/merged
  var-lib-docker-overlay2-755fc6e687d0794659ec603c835abea5ba84b86b7483a149017a4803a247c994-merged.mount loaded active mounted /var/lib/docker/overlay2/755fc6e687d0794659ec603c835abea5ba84b86b7483a149017a4803a247c994/merged
  var-lib-docker-overlay2-7ec5c1571be737403622444b538d83a0f3127ca0e78584c848a343f8d4dccd5f-merged.mount loaded active mounted /var/lib/docker/overlay2/7ec5c1571be737403622444b538d83a0f3127ca0e78584c848a343f8d4dccd5f/merged
  var-lib-docker-overlay2-95d9088b8990e8dd63cbef2b1432a2fd0a86f999269823709b727e024c372c87-merged.mount loaded active mounted /var/lib/docker/overlay2/95d9088b8990e8dd63cbef2b1432a2fd0a86f999269823709b727e024c372c87/merged
  var-lib-docker-overlay2-9b6c5a3c067dbc2c5984e557bfab12c8b36e4a545a1933e87c669cfb32af681b-merged.mount loaded active mounted /var/lib/docker/overlay2/9b6c5a3c067dbc2c5984e557bfab12c8b36e4a545a1933e87c669cfb32af681b/merged
  var-lib-docker-overlay2-bbb8daeaf00f1c02b3352e3b9ac34c68819111e55fa41c59bda035f251f7dd8e-merged.mount loaded active mounted /var/lib/docker/overlay2/bbb8daeaf00f1c02b3352e3b9ac34c68819111e55fa41c59bda035f251f7dd8e/merged
  var-lib-docker-overlay2-c78491bbca09dd2648f729cc3c4927db17448ebd8e0bba7a881a28db058de66a-merged.mount loaded active mounted /var/lib/docker/overlay2/c78491bbca09dd2648f729cc3c4927db17448ebd8e0bba7a881a28db058de66a/merged
  var-lib-docker-overlay2-e17b875fc839053a89ad6e671abb3402168174201f149a82e9239879548e9826-merged.mount loaded active mounted /var/lib/docker/overlay2/e17b875fc839053a89ad6e671abb3402168174201f149a82e9239879548e9826/merged
  var-lib-docker-overlay2-fa7c4b53b3da5313b4594c977fd5855831f909f40ce83f48ee24ad01007c3c81-merged.mount loaded active mounted /var/lib/docker/overlay2/fa7c4b53b3da5313b4594c977fd5855831f909f40ce83f48ee24ad01007c3c81/merged

Legend: LOAD   → Reflects whether the unit definition was properly loaded.
        ACTIVE → The high-level unit activation state, i.e. generalization of SUB.
        SUB    → The low-level unit activation state, values depend on unit type.

59 loaded units listed. Pass --all to see loaded but inactive units, too.
To show all installed unit files use 'systemctl list-unit-files'.

[0;34m===== OneDrive/rclone 설정 확인 =====[0m

### rclone 버전 ###
명령어: rclone version 2>/dev/null || echo 'rclone not found'
---
rclone v1.60.1-DEV
- os/version: ubuntu 24.04 (64 bit)
- os/kernel: 6.8.0-1024-oracle (aarch64)
- os/type: linux
- os/arch: arm64
- go/version: go1.22.2
- go/linking: dynamic
- go/tags: none

### rclone 설정 위치 ###
명령어: rclone config file 2>/dev/null || echo 'rclone not found'
---
Configuration file is stored at:
/home/<USER>/.config/rclone/rclone.conf

### rclone 리모트 목록 ###
명령어: rclone listremotes 2>/dev/null || echo 'rclone not found'
---
onedrive:

### OneDrive 마운트 포인트 검색 ###
발견된 OneDrive 마운트 포인트:
/home/<USER>/workspace

=== 마운트 포인트: /home/<USER>/workspace ===
### 디렉토리 권한 ###
명령어: ls -ld '/home/<USER>/workspace'
---
drwxrwxr-x 1 <USER> <GROUP> 0 May  4 13:37 /home/<USER>/workspace

### 상위 10개 항목 ###
명령어: ls -la '/home/<USER>/workspace' | head -10
---
total 59
drwxrwxr-x  1 <USER> <GROUP>     0 May  4 13:37 .
drwxr-x--- 28 <USER> <GROUP>  4096 May 29 21:32 ..
-rw-rw-r--  1 <USER> <GROUP>     0 Feb 10 03:25 .clinerules-webresearch
drwxrwxr-x  1 <USER> <GROUP>     0 Feb 26 00:27 00. Personal
drwxrwxr-x  1 <USER> <GROUP>     0 Apr 14 06:57 01. Project
drwxrwxr-x  1 <USER> <GROUP>     0 Mar 25 00:01 02. Research
drwxrwxr-x  1 <USER> <GROUP>     0 Mar  4 00:46 03. CompanyWork
drwxrwxr-x  1 <USER> <GROUP>     0 May  2 12:07 98. Todo
drwxrwxr-x  1 <USER> <GROUP>     0 Feb 20 22:53 98. Todo-MINIDESK

### 접근 권한 테스트 ###
명령어: test -r '/home/<USER>/workspace' && echo '읽기 가능' || echo '읽기 불가'
---
읽기 가능

### 쓰기 권한 테스트 ###
명령어: test -w '/home/<USER>/workspace' && echo '쓰기 가능' || echo '쓰기 불가'
---
쓰기 가능

### 실행 권한 테스트 ###
명령어: test -x '/home/<USER>/workspace' && echo '실행 가능' || echo '실행 불가'
---
실행 가능

[0;34m===== 관련 프로세스 정보 =====[0m

### rsync 프로세스 ###
명령어: ps aux | grep -E 'rsync|rclone' | grep -v grep
---
ubuntu      1541  0.0  0.0   2380  1408 ?        Ss   May04   0:00 /bin/sh -c rclone mount onedrive:workspace /home/<USER>/workspace --vfs-cache-mode writes
ubuntu      1563  0.0  0.2 2246432 65036 ?       Sl   May04  13:16 rclone mount onedrive:workspace /home/<USER>/workspace --vfs-cache-mode writes
root     1186535  0.0  0.0  12268  2104 ?        Ss   May26   0:00 rsync --daemon --config=/etc/rsyncd.conf

### FUSE 관련 프로세스 ###
명령어: ps aux | grep -i fuse | grep -v grep
---

### 마운트 관련 프로세스 ###
명령어: ps aux | grep -E 'mount|onedrive' | grep -v grep
---
ubuntu      1541  0.0  0.0   2380  1408 ?        Ss   May04   0:00 /bin/sh -c rclone mount onedrive:workspace /home/<USER>/workspace --vfs-cache-mode writes
ubuntu      1563  0.0  0.2 2246432 65036 ?       Sl   May04  13:16 rclone mount onedrive:workspace /home/<USER>/workspace --vfs-cache-mode writes

[0;34m===== 사용자 및 권한 정보 =====[0m

### 현재 사용자 정보 ###
명령어: id
---
uid=1001(ubuntu) gid=1001(ubuntu) groups=1001(ubuntu),4(adm),24(cdrom),27(sudo),30(dip),104(lxd),116(ssl-cert),988(docker)

### 사용자 그룹 ###
명령어: groups
---
ubuntu adm cdrom sudo dip lxd ssl-cert docker

### sudo 권한 ###
명령어: sudo -l 2>/dev/null || echo 'sudo 권한 확인 불가'
---
Matching Defaults entries for ubuntu on instance-*************:
    env_reset, mail_badpass, secure_path=/usr/local/sbin\:/usr/local/bin\:/usr/sbin\:/usr/bin\:/sbin\:/bin\:/snap/bin, use_pty

User ubuntu may run the following commands on instance-*************:
    (ALL : ALL) ALL
    (ALL) NOPASSWD: ALL

[0;34m===== 시스템 로그 (최근 OneDrive/rsync 관련) =====[0m

### 시스템 로그 - rsync ###
명령어: journalctl -xe | grep -i rsync | tail -20 2>/dev/null || dmesg | grep -i rsync | tail -20
---
May 29 21:38:29 instance-************* systemd[3108931]: rsync-workspace.service: Main process exited, code=exited, status=218/CAPABILITIES
May 29 21:38:29 instance-************* systemd[3108931]: rsync-workspace.service: Failed with result 'exit-code'.
May 29 21:38:39 instance-************* systemd[3108931]: rsync-workspace.service: Scheduled restart job, restart counter is at 81735.
May 29 21:38:39 instance-************* systemd[3108931]: Started rsync-workspace.service - User rsync daemon for OneDrive workspace.
May 29 21:38:39 instance-************* kernel: audit: type=1400 audit(1748554719.861:82033): apparmor="AUDIT" operation="userns_create" class="namespace" info="Userns create - transitioning profile" profile="unconfined" pid=1643267 comm="(rsync)" requested="userns_create" target="unprivileged_userns"
May 29 21:38:39 instance-************* (rsync)[1643267]: rsync-workspace.service: Failed to drop capabilities: Operation not permitted
May 29 21:38:39 instance-************* systemd[3108931]: rsync-workspace.service: Main process exited, code=exited, status=218/CAPABILITIES
May 29 21:38:39 instance-************* systemd[3108931]: rsync-workspace.service: Failed with result 'exit-code'.
May 29 21:38:50 instance-************* systemd[3108931]: rsync-workspace.service: Scheduled restart job, restart counter is at 81736.
May 29 21:38:50 instance-************* systemd[3108931]: Started rsync-workspace.service - User rsync daemon for OneDrive workspace.
May 29 21:38:50 instance-************* kernel: audit: type=1400 audit(1748554730.116:82034): apparmor="AUDIT" operation="userns_create" class="namespace" info="Userns create - transitioning profile" profile="unconfined" pid=1643501 comm="(rsync)" requested="userns_create" target="unprivileged_userns"
May 29 21:38:50 instance-************* (rsync)[1643501]: rsync-workspace.service: Failed to drop capabilities: Operation not permitted
May 29 21:38:50 instance-************* systemd[3108931]: rsync-workspace.service: Main process exited, code=exited, status=218/CAPABILITIES
May 29 21:38:50 instance-************* systemd[3108931]: rsync-workspace.service: Failed with result 'exit-code'.
May 29 21:39:00 instance-************* systemd[3108931]: rsync-workspace.service: Scheduled restart job, restart counter is at 81737.
May 29 21:39:00 instance-************* systemd[3108931]: Started rsync-workspace.service - User rsync daemon for OneDrive workspace.
May 29 21:39:00 instance-************* kernel: audit: type=1400 audit(1748554740.362:82035): apparmor="AUDIT" operation="userns_create" class="namespace" info="Userns create - transitioning profile" profile="unconfined" pid=1643733 comm="(rsync)" requested="userns_create" target="unprivileged_userns"
May 29 21:39:00 instance-************* (rsync)[1643733]: rsync-workspace.service: Failed to drop capabilities: Operation not permitted
May 29 21:39:00 instance-************* systemd[3108931]: rsync-workspace.service: Main process exited, code=exited, status=218/CAPABILITIES
May 29 21:39:00 instance-************* systemd[3108931]: rsync-workspace.service: Failed with result 'exit-code'.

### 시스템 로그 - rclone ###
명령어: journalctl -xe | grep -i rclone | tail -20 2>/dev/null || dmesg | grep -i rclone | tail -20
---

### 시스템 로그 - mount ###
명령어: journalctl -xe | grep -i 'mount\|fuse' | tail -20 2>/dev/null || dmesg | grep -i 'mount\|fuse' | tail -20
---

[0;34m===== rsync 설정 파일 =====[0m

### rsync 설정 파일 ###
명령어: ls -la /etc/rsync* 2>/dev/null || echo '설정 파일 없음'
---
-rw-r--r-- 1 <USER> <GROUP> 99 May 26 00:22 /etc/rsyncd.conf
-rw-r--r-- 1 <USER> <GROUP> 90 May 20 04:22 /etc/rsyncd.conf.backup.20250520042255

### 사용자 rsync 설정 ###
명령어: ls -la ~/.rsync* 2>/dev/null || echo '사용자 설정 없음'
---
사용자 설정 없음

[0;34m===== 보안 모듈 상태 =====[0m

### SELinux 상태 ###
명령어: getenforce 2>/dev/null || echo 'SELinux 미설치'
---
SELinux 미설치

### AppArmor 상태 ###
명령어: aa-status 2>/dev/null || systemctl status apparmor 2>/dev/null || echo 'AppArmor 미설치'
---
apparmor module is loaded.
● apparmor.service - Load AppArmor profiles
     Loaded: loaded (/usr/lib/systemd/system/apparmor.service; enabled; preset: enabled)
     Active: active (exited) since Sun 2025-05-04 13:37:47 UTC; 3 weeks 4 days ago
       Docs: man:apparmor(7)
             https://gitlab.com/apparmor/apparmor/wikis/home/
   Main PID: 867 (code=exited, status=0/SUCCESS)
        CPU: 232ms

May 04 13:37:48 instance-************* apparmor.systemd[867]: Restarting AppArmor
May 04 13:37:48 instance-************* apparmor.systemd[867]: /lib/apparmor/apparmor.systemd: 148: [: Illegal number: yes
May 04 13:37:48 instance-************* apparmor.systemd[867]: Reloading AppArmor profiles
May 04 13:37:47 instance-************* systemd[1]: Starting apparmor.service - Load AppArmor profiles...
May 04 13:37:47 instance-************* systemd[1]: Finished apparmor.service - Load AppArmor profiles.

[0;34m===== 커널 모듈 =====[0m

### FUSE 모듈 ###
명령어: lsmod | grep fuse
---

### 로드된 파일시스템 모듈 ###
명령어: cat /proc/filesystems
---
nodev	sysfs
nodev	tmpfs
nodev	bdev
nodev	proc
nodev	cgroup
nodev	cgroup2
nodev	cpuset
nodev	devtmpfs
nodev	configfs
nodev	debugfs
nodev	tracefs
nodev	securityfs
nodev	sockfs
nodev	bpf
nodev	pipefs
nodev	ramfs
nodev	hugetlbfs
nodev	devpts
	ext3
	ext2
	ext4
	squashfs
	vfat
nodev	ecryptfs
	fuseblk
nodev	fuse
nodev	fusectl
nodev	efivarfs
nodev	mqueue
nodev	pstore
	btrfs
nodev	autofs
nodev	binfmt_misc
nodev	rpc_pipefs
nodev	overlay


========================================
수집 완료: Thu May 29 21:39:02 UTC 2025
========================================
